import { <PERSON><PERSON>, User } from "lucide-react";
import { memo } from "react";
import { cn } from "../../../../shared/lib/shadcn/utils";

import { IChatMessage } from "../../types/messages.type";
import { <PERSON><PERSON><PERSON>enderer } from "../markdown";
import { TypingIndicator } from "./typing-indicador";

interface IChatMessageProps {
	message: IChatMessage;
}

const getMessageStyles = (isUser: boolean, isError?: boolean) => {
	if (isError) return "border-destructive/20 bg-destructive/10 text-destructive shadow-destructive/10";
	if (isUser) return "bg-primary text-primary-foreground shadow-primary/25 border border-primary/30";
	return "bg-muted/90 text-foreground shadow-accent/10 border border-border";
};

const getAvatarStyles = (isUser: boolean) => {
	if (isUser) return "bg-secondary border-border text-secondary-foreground";
	return "bg-accent border-border text-primary";
};

const formatTimestamp = (timestamp: Date) => {
	return new Date(timestamp).toLocaleTimeString([], {
		hour: "2-digit",
		minute: "2-digit",
	});
};

export const ChatMessage = memo<IChatMessageProps>(({ message }) => {
	const isUser = message.role === "user";
	const isAssistant = message.role === "assistant";
	const showTypingIndicator = isAssistant && message.id && !message.content.trim();

	if (showTypingIndicator) return <TypingIndicator />;

	return (
		<div className={cn("group hover:bg-accent/50 flex gap-2.5 px-3 py-2 transition-all duration-200", isUser ? "justify-end" : "justify-start")}>
			{isAssistant && (
				<div
					className={cn(
						"flex h-7 w-7 shrink-0 items-center justify-center rounded-full border shadow-sm transition-all duration-200 group-hover:scale-105 group-hover:shadow-md",
						getAvatarStyles(false),
					)}
				>
					<Bot className="h-3.5 w-3.5" />
				</div>
			)}
			<div className="flex max-w-[75%] flex-col gap-1">
				<div
					className={cn(
						"rounded-main px-3 py-2.5 text-sm shadow-sm backdrop-blur-sm transition-all duration-200 group-hover:shadow-md",
						getMessageStyles(isUser, message.isError),
					)}
				>
					<MarkdownRenderer content={message.content} isUser={isUser} className={cn("leading-relaxed break-words", isUser ? "user-message" : "")} />
				</div>

				{message.timestamp && (
					<div
						className={cn(
							"px-2 text-xs font-medium opacity-50 transition-opacity duration-200 group-hover:opacity-70",
							isUser ? "text-muted-foreground text-right" : "text-muted-foreground text-left",
						)}
					>
						{formatTimestamp(new Date(message.timestamp))}
					</div>
				)}
			</div>

			{isUser && (
				<div
					className={cn(
						"flex h-7 w-7 shrink-0 items-center justify-center rounded-full border shadow-sm transition-all duration-200 group-hover:scale-105 group-hover:shadow-md",
						getAvatarStyles(true),
					)}
				>
					<User className="h-3.5 w-3.5" />
				</div>
			)}
		</div>
	);
});
ChatMessage.displayName = "ChatMessage";
