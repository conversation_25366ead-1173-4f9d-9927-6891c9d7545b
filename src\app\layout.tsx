import { ProviderGlobal } from "@/config/providers/global";
import { ThemeScript } from "@/core/theme/components/theme-script";
import "@/modules/chat/components/markdown/markdown-styles.css";
import type { Metadata } from "next";
import { Montserrat } from "next/font/google";
import { headers } from "next/headers";
import "../layout/styles/index.css";
import ErrorWrapper from "./error-wapper";

const montserrat = Montserrat({ subsets: ["latin"] });

export const metadata: Metadata = {
	title: "S.I.M³P",
	description: "Sistema Integrado de Medição e Manufatura Pormade",
	icons: { icon: "/simp-favicon.svg" },
};

export default async function RootLayout({
	children,
}: Readonly<{
	children: React.ReactNode;
}>) {
	const h = await headers();
	const nonceWithMiddleware = h.get("x-nonce") || undefined;
	return (
		<html lang="pt-BR">
			<head>
				<ThemeScript nonce={nonceWithMiddleware} />
			</head>
			<body className={`${montserrat.className} antialiased`}>
				<ErrorWrapper>
					<ProviderGlobal>{children}</ProviderGlobal>
				</ErrorWrapper>
			</body>
		</html>
	);
}
