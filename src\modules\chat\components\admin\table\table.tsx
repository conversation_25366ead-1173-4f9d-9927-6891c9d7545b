"use client";
import { pathService } from "@/config/path-manager/service";
import { MainHeaderComponent } from "@/shared/components/custom/admin-header";
import { GenericEmptyState } from "@/shared/components/custom/empty";
import { BotIcon } from "lucide-react";
import { ComponentType, useState } from "react";
import { useTableChatAdmin } from "../../../hooks/list/table-chat-admin.hook";
import { EditKnowledgeModal } from "../edit/edit-knowledge-modal";
import { DesktopTableView } from "./desktop-table-view";
import { MobileTableView } from "./mobile-table-view";

interface TableChatAdminProps {
	onOpenModal: () => void;
}

export const TableChatAdmin = ({ onOpenModal }: TableChatAdminProps) => {
	const [searchTerm, setSearchTerm] = useState("");
	const item = pathService.getItemById("chat");
	const Icon = item?.icon as ComponentType<unknown> | undefined;
	const {
		data,
		isLoading,
		error,
		hasError,
		pagination,
		pageSize,
		isMobile,
		canUpdate,
		editKnowledge,
		handleRowClick,
		handlePageSizeChange,
		setCurrentPage,
		isEmpty,
	} = useTableChatAdmin({ searchTerm });

	const renderTable = () => {
		if (isMobile) {
			return (
				<>
					<MobileTableView
						data={data}
						isLoading={isLoading}
						hasError={hasError}
						error={error}
						searchTerm={searchTerm}
						pagination={pagination}
						onPageChange={setCurrentPage}
						onPageSizeChange={handlePageSizeChange}
					/>
				</>
			);
		} else {
			return (
				<>
					<DesktopTableView
						data={data}
						isLoading={isLoading}
						hasError={hasError}
						error={error}
						searchTerm={searchTerm}
						pageSize={pageSize}
						pagination={pagination}
						onPageChange={setCurrentPage}
						onPageSizeChange={handlePageSizeChange}
						onRowClick={handleRowClick}
						canUpdate={canUpdate}
					/>
					{editKnowledge.isOpen && editKnowledge.selectedId && (
						<EditKnowledgeModal isOpen={editKnowledge.isOpen} onClose={editKnowledge.closeEditModal} id={editKnowledge.selectedId} />
					)}
				</>
			);
		}
	};
	return (
		<main className="flex flex-1 flex-col gap-4">
			<MainHeaderComponent
				icon={Icon ? <Icon /> : <BotIcon />}
				title="Chat Admin"
				description="Gerencie o conhecimento do chatbot de IA"
				search={searchTerm}
				subject="all"
				onSearchChange={setSearchTerm}
				total={pagination?.totalItems || 0}
				onAdd={() => onOpenModal()}
				addButtonText="Novo conhecimento"
				searchPlaceholder="Buscar conhecimento"
				totalLabelSingular="conhecimento"
				totalLabelPlural="conhecimentos"
			/>
			<div className="h-full">
				{isEmpty || (data.length === 0 && !isLoading) ? (
					<GenericEmptyState
						title="Nenhum conhecimento ainda"
						description="Comece cadastrando conteúdos que o chatbot poderá usar para responder."
						buttonText="Adicionar conhecimento"
						onAction={() => onOpenModal()}
						subject="all"
						icon={BotIcon}
					/>
				) : (
					renderTable()
				)}
			</div>
		</main>
	);
};
