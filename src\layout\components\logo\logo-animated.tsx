"use client";
import { motion } from "framer-motion";
import { useState } from "react";

const LogoMinAnimated = ({ className }: { className?: string }) => {
	const [strokeWidth] = useState(4);
	return (
		<svg tabIndex={0} className={className} width="515" height="132" viewBox="0 0 515 132" fill="none" xmlns="http://www.w3.org/2000/svg">
			<g opacity="1">
				<motion.path
					d="M166.393 105.269V61.8587C166.393 61.1912 166.525 60.5303 166.783 59.9146C167.11 59.1339 167.629 58.4483 168.291 57.9213L168.616 57.6632C169.479 56.976 170.55 56.6018 171.654 56.6018H182.854C183.71 56.6018 184.541 56.8921 185.211 57.4252C185.41 57.5835 185.593 57.7614 185.756 57.9562L186.533 58.8837C186.74 59.1309 186.923 59.397 187.081 59.6786C187.509 60.445 187.733 61.3083 187.733 62.1862V107.549C187.733 108.249 187.594 108.943 187.323 109.589L187.289 109.672L186.854 110.45C186.56 110.976 186.094 111.385 185.533 111.608C185.224 111.731 184.895 111.794 184.563 111.794H171.272C170.416 111.794 169.585 111.504 168.915 110.971C168.716 110.813 168.533 110.635 168.37 110.44L168.021 110.023C167.531 109.438 167.132 108.783 166.837 108.08L166.72 107.661C166.503 106.882 166.393 106.078 166.393 105.269Z"
					fill="var(--color-logo-background)"
					stroke="var(--color-logo-background)"
					id="path-logo-1"
					strokeWidth={strokeWidth}
					initial={{
						pathLength: 0,
						fillOpacity: 0,
						strokeWidth: strokeWidth,
						stroke: "var(--color-logo-background)",
					}}
					animate={{
						pathLength: 1,
						fillOpacity: 1,
						stroke: "none",
					}}
					transition={{
						pathLength: { duration: 2, ease: "easeInOut" },
						fillOpacity: { duration: 0.6, delay: 2, ease: "easeInOut" },
						stroke: { duration: 1, delay: 2, ease: "easeInOut" },
					}}
					onUpdate={({ pathLength }) => {
						if (typeof pathLength === "number" && pathLength >= 0.9) {
							const currentPath = document.querySelector("#path-logo-1");
							if (currentPath) currentPath.setAttribute("stroke-width", "0.5");
						}
					}}
				/>
				<motion.path
					d="M267.625 106.956L259.522 98.6653C259.104 98.2374 258.531 97.9961 257.932 97.9961H236.639V72.5226L277.542 108.079H280.21L318.445 72.5226V97.9961H298.73C298.118 97.9961 297.532 98.2488 297.112 98.6944L289.299 106.985C287.962 108.403 288.967 110.733 290.916 110.733H331.451C331.954 110.733 332.442 110.562 332.836 110.249L334.363 109.034C334.71 108.758 334.967 108.384 335.1 107.961L336.127 104.69C336.195 104.474 336.229 104.25 336.229 104.024V66.1155C336.229 65.7915 336.158 65.4713 336.022 65.1775L334.227 61.3224C334.082 61.0099 333.866 60.7356 333.596 60.521L330.613 58.1466C330.219 57.8335 329.731 57.6631 329.228 57.6631H308.696C308.106 57.6631 307.541 57.8976 307.124 58.3151L280.21 85.2594H277.542L247.946 58.2442C247.536 57.8704 247.002 57.6631 246.447 57.6631H226.818C226.271 57.6631 225.743 57.8649 225.335 58.2298L222.689 60.5994C222.505 60.7635 222.351 60.9569 222.231 61.1717L220.026 65.1183C219.841 65.4496 219.744 65.8228 219.744 66.2023V103.918C219.744 104.213 219.803 104.505 219.917 104.777L221.24 107.935C221.423 108.374 221.743 108.742 222.151 108.985L224.553 110.419C224.897 110.624 225.291 110.733 225.692 110.733H266.036C268.004 110.733 269.001 108.364 267.625 106.956Z"
					fill="var(--color-logo-background)"
					id="path-logo-2"
					stroke="var(--color-logo-background)"
					strokeWidth={strokeWidth}
					initial={{
						pathLength: 0,
						fillOpacity: 0,
						strokeWidth: strokeWidth,
						stroke: "var(--color-logo-background)",
					}}
					animate={{
						pathLength: 1,
						fillOpacity: 1,
						stroke: "none",
					}}
					transition={{
						pathLength: { duration: 2, ease: "easeInOut" },
						fillOpacity: { duration: 0.6, delay: 2, ease: "easeInOut" },
						stroke: { duration: 1, delay: 2, ease: "easeInOut" },
					}}
					onUpdate={({ pathLength }) => {
						if (typeof pathLength === "number" && pathLength >= 0.9) {
							const currentPath = document.querySelector("#path-logo-2");
							if (currentPath) currentPath.setAttribute("stroke-width", "0.5");
						}
					}}
				/>
				<motion.path
					d="M368.24 107.489V80.2851C368.24 79.4205 368.695 78.6197 369.437 78.1766C369.818 77.9496 370.253 77.8297 370.696 77.8297H419.401C420.258 77.8297 421.104 77.638 421.878 77.2688L423.712 76.3929C424.59 75.9737 425.149 75.0875 425.149 74.1148C425.149 73.142 424.59 72.2558 423.712 71.8367L421.878 70.9608C421.104 70.5915 420.258 70.3999 419.401 70.3999H372.081C369.96 70.3999 368.24 68.6803 368.24 66.559C368.24 65.6148 368.588 64.7037 369.217 63.9997L372.702 60.0994C374.389 58.2116 376.801 57.1324 379.333 57.1324H436.79C439.543 57.1324 442.141 58.4073 443.824 60.5848L444.632 61.629C445.836 63.1865 446.49 65.0997 446.49 67.0686V80.9656C446.49 83.0537 445.755 85.0752 444.414 86.6759L443.82 87.3847C442.131 89.4014 439.635 90.5664 437.004 90.5664H392.027C391.04 90.5664 390.104 91.0034 389.47 91.7598C388.967 92.3602 388.692 93.1184 388.692 93.9017V107.489C388.692 108.224 388.504 108.947 388.145 109.589C387.385 110.95 385.947 111.794 384.387 111.794H372.545C370.985 111.794 369.547 110.95 368.787 109.589C368.428 108.947 368.24 108.224 368.24 107.489Z"
					fill="var(--color-logo-background)"
					stroke="var(--color-logo-background)"
					strokeWidth={strokeWidth}
					id="path-logo-3"
					initial={{
						pathLength: 0,
						fillOpacity: 0,
						strokeWidth: strokeWidth,
						stroke: "var(--color-logo-background)",
					}}
					animate={{
						pathLength: 1,
						fillOpacity: 1,
						stroke: "none",
					}}
					transition={{
						pathLength: { duration: 2, ease: "easeInOut" },
						fillOpacity: { duration: 0.6, delay: 2, ease: "easeInOut" },
						stroke: { duration: 1, delay: 2, ease: "easeInOut" },
					}}
					onUpdate={({ pathLength }) => {
						if (typeof pathLength === "number" && pathLength >= 0.9) {
							const currentPath = document.querySelector("#path-logo-3");
							if (currentPath) currentPath.setAttribute("stroke-width", "0.5");
						}
					}}
				/>
				<motion.path
					d="M127.811 20.5143H30.1141C29.6749 20.5143 29.2363 20.5468 28.8019 20.6116L26.6937 20.9262C26.1641 21.0052 25.6426 21.1319 25.1357 21.3048L24.2452 21.6085C23.2792 21.938 22.3772 22.4312 21.5786 23.0667L18.3336 25.6489C17.747 26.1157 17.2218 26.6549 16.7705 27.2535L14.5166 30.2427C13.8117 31.1777 13.2983 32.2426 13.0061 33.3765L11.9519 37.4661C11.765 38.1911 11.6704 38.9369 11.6704 39.6856V69.1091C11.6704 69.9632 11.818 70.8109 12.1066 71.6147L12.411 72.4626C12.7945 73.5308 13.537 74.4328 14.5116 75.0144C15.2709 75.4676 16.1387 75.7069 17.0229 75.7069H90.9755C92.0239 75.7069 93.0459 76.0351 93.8982 76.6455C94.499 77.0758 94.9975 77.6335 95.3578 78.2788L95.7546 78.9891C96.3055 79.9756 96.6581 81.0604 96.7924 82.1823L96.8254 82.4585C96.9625 83.6039 96.8435 84.7654 96.4769 85.8592L96.3485 86.2423C96.2131 86.6466 96.0241 87.031 95.7867 87.3852C94.8988 88.71 93.409 89.505 91.8142 89.505H4.6116C3.9938 89.505 3.3875 89.6722 2.85699 89.9888C2.22839 90.364 1.73659 90.9307 1.45376 91.6059L1.33708 91.8845C1.11459 92.4156 1 92.9858 1 93.5617V106.676C1 107.252 1.11459 107.822 1.33708 108.353L1.45376 108.632C1.73659 109.307 2.22839 109.874 2.85699 110.249C3.3875 110.566 3.99379 110.733 4.6116 110.733H115.185C116.12 110.733 117.049 110.586 117.938 110.296L120.179 109.566C121.046 109.284 121.864 108.871 122.604 108.34L124.316 107.114C125.381 106.352 126.264 105.364 126.902 104.221L128.6 101.18L129.649 99.0936C129.839 98.7163 130.001 98.3259 130.136 97.9254L130.362 97.2485C130.668 96.3378 130.823 95.3836 130.823 94.4231V60.9512C130.823 60.5293 130.78 60.1085 130.693 59.6956L130.633 59.4069C130.466 58.6105 130.105 57.8675 129.583 57.2438L129.551 57.2056C129.216 56.8059 128.816 56.4653 128.368 56.198C127.647 55.7676 126.823 55.5403 125.983 55.5403H125.488H48.528C48.2635 55.5403 48.0013 55.4901 47.7555 55.3923C47.1495 55.1512 46.6908 54.6409 46.5154 54.0127L46.4455 53.7625C46.3816 53.5336 46.3492 53.2971 46.3492 53.0594V45.2845C46.3492 45.0469 46.3816 44.8103 46.4455 44.5814L46.5154 44.3312C46.6908 43.703 47.1495 43.1928 47.7555 42.9516C48.0013 42.8538 48.2635 42.8036 48.528 42.8036H126.959C127.737 42.8036 128.496 42.56 129.128 42.1071C129.929 41.5335 130.47 40.666 130.633 39.6945L130.749 39.0029C130.798 38.7068 130.823 38.4071 130.823 38.1069V23.9986C130.823 23.7989 130.807 23.5995 130.774 23.4025L130.699 22.959C130.505 21.7988 129.635 20.8675 128.491 20.5943C128.268 20.5411 128.04 20.5143 127.811 20.5143Z"
					fill="var(--color-logo-background)"
					fillOpacity="0"
					stroke="var(--color-logo-background)"
					strokeWidth={strokeWidth}
					id={"path-logo-4"}
					initial={{
						pathLength: 0,
						fillOpacity: 0,
						strokeWidth: strokeWidth,
						stroke: "var(--color-logo-background)",
					}}
					animate={{
						pathLength: 1,
						fillOpacity: 1,
						stroke: "none",
					}}
					transition={{
						pathLength: { duration: 2, ease: "easeInOut" },
						fillOpacity: { duration: 0.6, delay: 2, ease: "easeInOut" },
						stroke: { duration: 1, delay: 2, ease: "easeInOut" },
					}}
					onUpdate={({ pathLength }) => {
						if (typeof pathLength === "number" && pathLength >= 0.9) {
							const currentPath = document.querySelector("#path-logo-4");

							if (currentPath) {
								currentPath.setAttribute("stroke-width", "0.5");
							}
						}
					}}
				/>
				<motion.ellipse
					cx="146.385"
					cy="104.365"
					rx="7.1136"
					id="path-logo-animation"
					ry="7.96047"
					fill="var(--color-logo-background)"
					initial={{ scale: 0, opacity: 0, y: -20 }}
					animate={{ scale: 1, opacity: 1, y: 0 }}
					transition={{
						type: "spring",
						stiffness: 500,
						damping: 30,
						duration: 1,
						delay: 2,
					}}
				/>
				<motion.ellipse
					cx="199.737"
					cy="104.365"
					rx="7.1136"
					id="path-logo-animation"
					ry="7.96047"
					fill="var(--color-logo-background)"
					initial={{ scale: 0, opacity: 0, y: -20 }}
					animate={{ scale: 1, opacity: 1, y: 0 }}
					transition={{
						type: "spring",
						stiffness: 500,
						damping: 30,
						duration: 1,
						delay: 2,
					}}
				/>
				<motion.ellipse
					cx="348.233"
					cy="104.365"
					id="path-logo-animation"
					rx="7.1136"
					ry="7.96047"
					fill="var(--color-logo-background)"
					initial={{ scale: 0, opacity: 0, y: -20 }}
					animate={{ scale: 1, opacity: 1, y: 0 }}
					transition={{
						type: "spring",
						stiffness: 500,
						damping: 30,
						duration: 1,
						delay: 2,
					}}
				/>
				<motion.ellipse
					cx="339.518"
					cy="50.2334"
					rx="2.84544"
					id="path-logo-animation"
					ry="3.18419"
					fill="var(--color-logo-background)"
					initial={{ scale: 0, opacity: 0, y: -20 }}
					animate={{ scale: 1, opacity: 1, y: 0 }}
					transition={{
						type: "spring",
						stiffness: 500,
						damping: 30,
						duration: 0.5,
						delay: 2,
					}}
				/>
				<motion.path
					d="M433.364 51.6432C431.378 51.5616 429.823 50.713 428.269 49.8481C425.852 48.5099 424.279 46.5189 423.098 44.3158C422.48 43.1245 422.143 41.7863 421.881 40.4808C421.618 39.2242 421.487 37.9187 421.487 36.6458C421.525 33.0719 422.218 29.5795 423.379 26.1525C425.047 21.2241 427.22 16.4589 429.898 11.8895C430.854 10.2575 431.36 8.5277 431.36 6.73258C431.36 5.63919 431.004 4.5458 430.798 3.43609C430.723 3.06075 430.666 2.66909 430.554 2C432.24 2.71805 433.776 3.22394 435.125 3.95831C437.729 5.3944 440.389 6.79786 442.73 8.5277C445.334 10.4371 447.732 12.5912 449.98 14.8106C451.647 16.4589 453.09 18.3356 454.345 20.2449C455.675 22.3175 456.705 24.5695 457.024 26.9521C457.305 29.09 457.529 31.2604 457.361 33.3982C457.23 35.2586 456.593 37.1027 456.012 38.9305C455.244 41.4273 453.708 43.5978 451.91 45.6214C450.074 47.6939 447.788 49.3585 445.222 50.6803C443.442 51.5942 441.55 52.3286 439.452 53.2425C439.527 52.4591 439.583 51.9696 439.64 51.4637C440.014 48.6731 440.351 45.8662 440.764 43.0592C441.494 38.1472 441.457 33.2024 441.269 28.2577C441.157 24.6511 440.67 21.0119 439.246 17.5686C438.703 16.2793 437.916 15.0554 437.186 13.8151C437.054 13.5867 436.717 13.4235 436.474 13.2276C436.399 13.2712 436.324 13.3147 436.249 13.3582C436.399 14.2394 436.53 15.1207 436.68 16.0019C436.905 17.3727 437.279 18.7272 437.373 20.0817C437.616 23.7209 437.935 27.3438 437.897 30.983C437.86 33.7736 437.354 36.5479 437.017 39.3385C436.605 42.7329 435.593 45.9967 434.394 49.2279C434.095 50.0276 433.757 50.8109 433.364 51.6432Z"
					fill="var(--color-leaf-green-color)"
					stroke="var(--color-leaf-green-color)"
					id="path-logo-5"
					initial={{ scale: 0, opacity: 0, y: -20 }}
					animate={{ scale: 1, opacity: 1, y: 0 }}
					transition={{
						type: "spring",
						stiffness: 500,
						damping: 30,
						duration: 1,
						delay: 2,
						fillOpacity: { duration: 0.6, delay: 2, ease: "easeInOut" },
					}}
					onUpdate={({ pathLength }) => {
						if (typeof pathLength === "number" && pathLength >= 0.9) {
							const currentPath = document.querySelector("#path-logo-5");
							if (currentPath) {
								currentPath.setAttribute("stroke-width", "0.5");
							}
						}
					}}
					onAnimationComplete={() => {
						const currentPath = document.querySelector("#path-logo-5");
						if (currentPath) {
							currentPath.setAttribute("stroke-width", "0.5");
						}
					}}
				/>
				<motion.path
					d="M486.555 10.0268C489.181 9.99367 491.826 10.209 494.542 10.325C494.974 10.3747 495.334 10.4079 495.657 10.4907C496.611 10.7723 497.6 11.0043 498.482 11.4185C499.201 11.7333 499.201 12.4457 498.787 12.9924C497.474 14.7154 496.071 16.4053 494.956 18.2443C493.643 20.4312 492.509 22.7175 491.502 25.0204C490.477 27.3564 489.523 29.7421 488.858 32.1776C488.012 35.2591 486.861 38.175 485.008 40.8424C482.202 44.9345 478.478 48.1486 473.801 50.3521C470.006 52.158 467.032 53.7326 462.57 53.2687C462.409 53.2522 459.572 53.2522 459.41 53.2687C459.41 53.2687 458.349 53.4999 457.18 52.5887C456.28 51.8763 456.011 51.2136 456.496 50.2858C457.108 49.1427 457.702 47.9664 458.529 46.9392C460.652 44.3215 462.81 41.6873 465.149 39.2022C467.631 36.5514 470.312 34.0331 472.956 31.4817C474.665 29.825 476.464 28.2179 478.244 26.6109C479.504 25.5009 480.835 24.4571 482.094 23.3471C483.497 22.1377 484.864 20.9117 486.249 19.7022C486.213 19.636 486.177 19.5642 486.141 19.4868C485.476 19.7188 484.756 19.901 484.109 20.1993C480.457 21.9388 476.841 23.7447 473.657 26.1801C471.445 27.87 469.178 29.5102 467.056 31.316C465.545 32.5752 464.177 34 462.9 35.4579C460.652 38.0093 458.439 40.5938 456.406 43.2778C454.949 45.183 453.78 47.2871 452.521 49.3249C452.521 49.7471 449.932 53.2687 449.932 53.2687L445.191 57.6632C446.771 57.6632 451.512 56.1984 456.252 56.1984C460.993 56.1984 464.789 56.8466 467.811 56.2667C471.229 55.6206 474.449 54.4774 477.489 52.9035C479.899 51.6444 482.22 50.2196 484.199 48.4303C485.566 47.1712 486.879 45.8623 488.102 44.5038C490.872 41.4222 492.671 37.8602 493.265 33.9171C493.715 30.7859 494.848 27.9197 496.269 25.1198C497.906 21.8394 499.993 18.7744 502.313 15.8751C503.752 14.0858 505.281 12.3463 506.774 10.5901C507.386 9.8777 507.314 9.56291 506.36 9.33097C504.526 8.88365 502.691 8.38662 500.802 8.08841C498.338 7.69079 495.819 7.3263 493.319 7.21033C490.063 7.06122 486.789 7.04465 483.569 7.67422C481.159 8.13811 478.712 8.50259 476.356 9.13216C472.578 10.1428 468.998 11.6007 465.581 13.4563C462.828 14.9474 460.184 16.5544 457.917 18.6419C457.288 19.2218 457.36 19.5034 458.205 19.7685C458.493 19.8513 458.817 19.9176 459.141 19.9342C461.731 20.0501 464.105 19.2549 466.3 18.1614C469.538 16.5378 472.686 14.732 475.816 12.893C479.108 10.9712 482.706 10.0599 486.555 10.0268Z"
					fill="var(--color-leaf-green-color)"
					stroke="var(--color-leaf-green-color)"
					id="path-logo-6"
					initial={{ scale: 0, opacity: 0, y: -20 }}
					animate={{ scale: 1, opacity: 1, y: 0 }}
					transition={{
						type: "spring",
						stiffness: 500,
						damping: 30,
						duration: 1,
						delay: 2,
						fillOpacity: { duration: 0.6, delay: 2, ease: "easeInOut" },
					}}
					onUpdate={({ pathLength }) => {
						if (typeof pathLength === "number" && pathLength >= 0.9) {
							const currentPath = document.querySelector("#path-logo-6");
							if (currentPath) currentPath.setAttribute("stroke-width", "0.5");
						}
					}}
				/>
				<motion.path
					d="M309.602 35H329.404L329.9 35.0695C330.046 35.0901 330.191 35.1231 330.332 35.1683C330.606 35.2563 330.864 35.3893 331.095 35.5619L331.529 35.8865C331.722 36.0312 331.897 36.1998 332.048 36.3882C332.424 36.8565 332.643 37.431 332.674 38.0305L333 44.1892L333 49.0687C333 49.7684 332.72 50.4391 332.222 50.9312C331.957 51.1935 331.639 51.3962 331.289 51.5256L330.746 51.7266L330.12 51.8769C329.643 51.9588 329.161 52 328.677 52H309.604C309.529 52 309.454 51.9857 309.384 51.958C309.251 51.9056 309.142 51.8079 309.075 51.6824L309.072 51.6764C309.025 51.5872 309 51.4877 309 51.3867L309 48.4791C309 48.3776 309.026 48.2778 309.076 48.1894C309.143 48.0706 309.248 47.9785 309.375 47.9285L309.417 47.9118C309.501 47.8786 309.591 47.8615 309.681 47.8615H325.658C325.709 47.8615 325.759 47.8525 325.806 47.8348C325.93 47.7884 326.025 47.6865 326.063 47.5596L326.073 47.528C326.086 47.4828 326.093 47.436 326.093 47.3889V45.9245C326.093 45.8774 326.086 45.8305 326.073 45.7854L326.063 45.7538C326.025 45.6269 325.93 45.525 325.806 45.4786C325.759 45.4609 325.709 45.4519 325.658 45.4519H312.565C312.417 45.4519 312.274 45.408 312.151 45.3258C311.985 45.2139 311.871 45.0392 311.836 44.8419L311.817 44.7352C311.807 44.6776 311.802 44.6192 311.802 44.5607L311.802 42.4089C311.802 42.2283 311.868 42.0541 311.988 41.9193C312.043 41.8584 312.107 41.807 312.178 41.7671L312.21 41.749C312.353 41.6687 312.514 41.6266 312.678 41.6266H312.786H325.569C325.619 41.6266 325.67 41.6175 325.717 41.5998C325.841 41.5534 325.936 41.4515 325.974 41.3246L325.983 41.293C325.996 41.2479 326.003 41.201 326.003 41.1539V39.6895C326.003 39.6424 325.996 39.5956 325.983 39.5504L325.974 39.5189C325.936 39.3919 325.841 39.29 325.717 39.2436C325.67 39.226 325.619 39.2169 325.569 39.2169H309.763C309.616 39.2169 309.472 39.173 309.35 39.0908C309.183 38.9789 309.07 38.8043 309.034 38.607L309.015 38.5003C309.005 38.4427 309 38.3842 309 38.3257V35.6623C309 35.6224 309.004 35.5827 309.011 35.5435L309.021 35.4874C309.062 35.2529 309.241 35.0665 309.473 35.0143C309.516 35 309.559 35 309.602 35Z"
					fill="var(--color-logo-background)"
					id="path-logo-7"
					stroke="var(--color-logo-background)"
					initial={{ scale: 0, opacity: 0, y: -20 }}
					animate={{ scale: 1, opacity: 1, y: 0 }}
					transition={{
						type: "spring",
						stiffness: 500,
						damping: 30,
						duration: 1,
						delay: 2,
						fillOpacity: { duration: 0.6, delay: 2, ease: "easeInOut" },
					}}
				/>
				<motion.path
					fillRule="evenodd"
					id="path-logo-animation"
					clipRule="evenodd"
					d="M344.762 42.9722C344.762 43.0632 344.762 43.1548 344.762 43.2469C344.759 48.3879 345.242 51.4273 357.764 51.4273C370.286 51.4273 370.298 49.3648 370.296 43.8287C370.294 38.2925 369.076 34.7103 356.808 34.7103C344.759 34.7103 344.76 37.9854 344.762 42.9722ZM351.766 42.5913C351.766 42.624 351.766 42.6563 351.766 42.6879C351.766 42.7502 351.766 42.8156 351.766 42.8837C351.758 44.8294 351.742 49.0392 357.429 49.0392C363.315 49.0392 363.77 46.253 363.769 43.0688C363.769 39.8847 363.196 37.0985 357.429 37.0985C351.761 37.0985 351.764 40.7186 351.766 42.5913Z"
					fill="var(--color-logo-background)"
					initial={{ scale: 0, opacity: 0, y: -20 }}
					animate={{ scale: 1, opacity: 1, y: 0 }}
					transition={{
						type: "spring",
						stiffness: 500,
						damping: 30,
						duration: 1,
						delay: 2,
						fillOpacity: { duration: 0.6, delay: 2, ease: "easeInOut" },
					}}
				/>
				<motion.path
					id="path-logo-animation"
					d="M344.762 43.2469L345.712 43.2474L344.762 43.2469ZM344.762 42.9722L345.712 42.9719L344.762 42.9722ZM351.766 42.5913L350.816 42.5922L350.816 42.5922L351.766 42.5913ZM351.766 42.8837L350.815 42.88L350.815 42.88L351.766 42.8837ZM363.769 43.0688L364.72 43.0686L364.72 43.0686L363.769 43.0688ZM345.712 43.2474C345.712 43.1548 345.712 43.0628 345.712 42.9719L343.811 42.9724C343.811 43.0635 343.811 43.1547 343.811 43.2465L345.712 43.2474ZM357.764 50.477C354.664 50.477 352.356 50.2882 350.638 49.9406C348.916 49.5925 347.851 49.0978 347.174 48.5344C345.876 47.4544 345.711 45.8806 345.712 43.2474L343.811 43.2465C343.81 45.7542 343.885 48.2706 345.958 49.9955C346.967 50.8347 348.37 51.4211 350.261 51.8035C352.154 52.1865 354.603 52.3776 357.764 52.3776V50.477ZM369.345 43.829C369.346 45.2441 369.341 46.3046 369.168 47.1632C369.004 47.9714 368.705 48.5207 368.146 48.9511C367.546 49.4131 366.555 49.8101 364.856 50.08C363.168 50.3481 360.877 50.477 357.764 50.477V52.3776C360.911 52.3776 363.318 52.2487 365.154 51.9571C366.979 51.6672 368.338 51.2026 369.306 50.457C370.315 49.6798 370.801 48.6755 371.031 47.5399C371.25 46.4547 371.247 45.1813 371.246 43.8284L369.345 43.829ZM356.808 35.6607C362.92 35.6607 365.975 36.569 367.531 37.9127C369.02 39.1994 369.345 41.0737 369.345 43.829L371.246 43.8284C371.245 41.0475 370.96 38.3626 368.773 36.4742C366.652 34.6428 362.963 33.76 356.808 33.76V35.6607ZM345.712 42.9719C345.712 41.7013 345.715 40.6563 345.883 39.7516C346.046 38.8786 346.353 38.204 346.923 37.6575C347.509 37.0969 348.457 36.5893 350.053 36.2267C351.65 35.8639 353.83 35.6607 356.808 35.6607V33.76C353.761 33.76 351.423 33.9662 349.632 34.3733C347.84 34.7805 346.529 35.4034 345.609 36.2849C344.674 37.1805 344.228 38.2597 344.015 39.4033C343.807 40.5152 343.811 41.7496 343.811 42.9724L345.712 42.9719ZM352.717 42.6879C352.717 42.6557 352.717 42.623 352.717 42.5903L350.816 42.5922C350.816 42.625 350.816 42.6568 350.816 42.6879H352.717ZM352.716 42.8874C352.716 42.8197 352.717 42.7524 352.717 42.6879H350.816C350.816 42.748 350.816 42.8115 350.815 42.88L352.716 42.8874ZM357.429 48.0889C356.122 48.0889 355.219 47.8471 354.59 47.5122C353.97 47.1821 353.568 46.7358 353.296 46.2365C352.724 45.1862 352.712 43.8917 352.716 42.8874L350.815 42.88C350.812 43.8214 350.788 45.6047 351.627 47.1457C352.061 47.9421 352.721 48.6698 353.697 49.1898C354.664 49.7051 355.892 49.9895 357.429 49.9895V48.0889ZM362.819 43.069C362.819 44.6797 362.684 45.8565 362.056 46.6654C361.472 47.4175 360.255 48.0889 357.429 48.0889V49.9895C360.489 49.9895 362.442 49.2678 363.557 47.8307C364.629 46.4505 364.72 44.6422 364.72 43.0686L362.819 43.069ZM357.429 38.0488C360.187 38.0488 361.414 38.7163 362.016 39.4785C362.658 40.2919 362.819 41.4768 362.819 43.069L364.72 43.0686C364.719 41.4768 364.593 39.6764 363.507 38.3007C362.381 36.8737 360.438 36.1482 357.429 36.1482V38.0488ZM352.717 42.5903C352.716 41.6171 352.735 40.5188 353.279 39.644C353.76 38.8692 354.809 38.0488 357.429 38.0488V36.1482C354.381 36.1482 352.598 37.1378 351.664 38.6413C350.793 40.0447 350.815 41.6927 350.816 42.5922L352.717 42.5903Z"
					fill="var(--color-logo-background)"
					initial={{ scale: 0, opacity: 0, y: -20 }}
					animate={{ scale: 1, opacity: 1, y: 0 }}
					transition={{
						type: "spring",
						stiffness: 500,
						damping: 30,
						duration: 1,
						delay: 2,
						fillOpacity: { duration: 0.6, delay: 2, ease: "easeInOut" },
					}}
				/>
				<motion.path
					id="path-logo-animation"
					d="M404.439 117.851V102.699L405.426 106.114L401.184 107.928L400.411 102.753L405.959 100.219H410.014V117.851H404.439ZM400.678 119.159V114.25H413.856V119.159H400.678ZM422.11 119.425C420.242 119.425 418.66 119.052 417.361 118.305C416.063 117.558 415.076 116.473 414.4 115.05C413.725 113.61 413.387 111.867 413.387 109.822C413.387 107.777 413.725 106.043 414.4 104.62C415.076 103.18 416.063 102.086 417.361 101.339C418.66 100.592 420.242 100.219 422.11 100.219C423.977 100.219 425.56 100.592 426.858 101.339C428.174 102.086 429.17 103.18 429.846 104.62C430.522 106.043 430.859 107.777 430.859 109.822C430.859 111.867 430.522 113.61 429.846 115.05C429.17 116.473 428.174 117.558 426.858 118.305C425.56 119.052 423.977 119.425 422.11 119.425ZM422.11 114.677C423.159 114.677 423.968 114.259 424.537 113.423C425.106 112.587 425.391 111.387 425.391 109.822C425.391 108.168 425.106 106.905 424.537 106.034C423.968 105.145 423.159 104.7 422.11 104.7C421.06 104.7 420.251 105.145 419.682 106.034C419.113 106.905 418.829 108.168 418.829 109.822C418.829 111.387 419.113 112.587 419.682 113.423C420.251 114.259 421.06 114.677 422.11 114.677ZM435.24 119.159L442.736 100.485H447.271L454.714 119.159H449.005L446.017 110.996C445.822 110.444 445.653 109.973 445.51 109.582C445.368 109.191 445.244 108.826 445.137 108.488C445.03 108.132 444.924 107.759 444.817 107.368C444.71 106.959 444.603 106.479 444.497 105.927H445.404C445.297 106.479 445.19 106.959 445.084 107.368C444.977 107.759 444.861 108.132 444.737 108.488C444.63 108.826 444.497 109.191 444.337 109.582C444.194 109.973 444.025 110.444 443.83 110.996L440.842 119.159H435.24ZM438.975 116.384L440.655 112.463H449.325L450.846 116.384H438.975ZM453.658 119.159V100.485H458.806L467.396 112.356L466.169 111.983C466.08 111.271 466 110.649 465.929 110.115C465.876 109.582 465.822 109.093 465.769 108.648C465.733 108.186 465.698 107.732 465.662 107.288C465.644 106.843 465.636 106.354 465.636 105.821C465.636 105.287 465.636 104.665 465.636 103.953V100.485H471.184V119.159H465.982L456.512 106.087L458.673 106.728C458.762 107.386 458.833 107.964 458.887 108.461C458.958 108.942 459.011 109.386 459.047 109.795C459.082 110.204 459.109 110.613 459.127 111.022C459.162 111.414 459.18 111.858 459.18 112.356C459.198 112.836 459.207 113.405 459.207 114.063V119.159H453.658ZM481.811 119.425C480.335 119.425 478.984 119.185 477.757 118.705C476.53 118.225 475.463 117.549 474.556 116.678C473.649 115.806 472.946 114.775 472.448 113.583C471.968 112.392 471.728 111.094 471.728 109.689C471.728 108.266 471.968 106.968 472.448 105.794C472.946 104.602 473.649 103.571 474.556 102.699C475.463 101.828 476.53 101.152 477.757 100.672C478.984 100.192 480.326 99.9518 481.785 99.9518C483.261 99.9518 484.603 100.192 485.813 100.672C487.04 101.152 488.107 101.828 489.014 102.699C489.921 103.571 490.614 104.602 491.095 105.794C491.593 106.968 491.842 108.257 491.842 109.662C491.842 111.085 491.593 112.392 491.095 113.583C490.614 114.775 489.921 115.806 489.014 116.678C488.107 117.549 487.04 118.225 485.813 118.705C484.603 119.185 483.27 119.425 481.811 119.425ZM481.785 114.09C482.389 114.09 482.941 113.983 483.439 113.77C483.954 113.557 484.399 113.254 484.772 112.863C485.164 112.454 485.457 111.983 485.653 111.449C485.866 110.916 485.973 110.329 485.973 109.689C485.973 109.048 485.866 108.461 485.653 107.928C485.457 107.394 485.164 106.932 484.772 106.541C484.399 106.132 483.954 105.821 483.439 105.607C482.941 105.394 482.389 105.287 481.785 105.287C481.18 105.287 480.62 105.394 480.104 105.607C479.606 105.821 479.162 106.132 478.77 106.541C478.397 106.932 478.103 107.394 477.89 107.928C477.694 108.461 477.597 109.048 477.597 109.689C477.597 110.329 477.694 110.916 477.89 111.449C478.103 111.983 478.397 112.454 478.77 112.863C479.162 113.254 479.606 113.557 480.104 113.77C480.62 113.983 481.18 114.09 481.785 114.09ZM498.91 119.425C497.505 119.425 496.26 119.274 495.176 118.972C494.109 118.652 493.166 118.207 492.348 117.638C491.548 117.069 490.827 116.402 490.187 115.637L493.522 111.903C494.411 113.041 495.336 113.797 496.296 114.17C497.274 114.526 498.19 114.704 499.044 114.704C499.382 114.704 499.684 114.677 499.951 114.624C500.217 114.553 500.422 114.446 500.564 114.304C500.706 114.161 500.778 113.966 500.778 113.717C500.778 113.485 500.698 113.29 500.538 113.13C500.395 112.97 500.2 112.836 499.951 112.73C499.702 112.605 499.426 112.507 499.124 112.436C498.839 112.347 498.555 112.276 498.27 112.223C498.003 112.169 497.763 112.116 497.55 112.063C496.483 111.814 495.549 111.511 494.749 111.156C493.949 110.8 493.282 110.373 492.748 109.875C492.215 109.377 491.814 108.808 491.548 108.168C491.299 107.528 491.174 106.808 491.174 106.007C491.174 105.1 491.379 104.273 491.788 103.526C492.215 102.78 492.775 102.139 493.468 101.606C494.18 101.072 494.98 100.663 495.869 100.379C496.776 100.094 497.71 99.9518 498.67 99.9518C500.075 99.9518 501.249 100.085 502.191 100.352C503.134 100.601 503.925 100.966 504.566 101.446C505.206 101.926 505.757 102.486 506.22 103.126L502.858 106.354C502.467 105.981 502.058 105.678 501.631 105.447C501.204 105.198 500.76 105.02 500.297 104.914C499.853 104.789 499.408 104.727 498.964 104.727C498.555 104.727 498.208 104.762 497.923 104.834C497.639 104.887 497.416 104.985 497.256 105.127C497.096 105.251 497.016 105.429 497.016 105.661C497.016 105.892 497.114 106.087 497.31 106.247C497.523 106.39 497.781 106.514 498.083 106.621C498.403 106.728 498.715 106.816 499.017 106.888C499.337 106.941 499.604 106.985 499.817 107.021C500.795 107.199 501.694 107.448 502.512 107.768C503.33 108.07 504.041 108.461 504.646 108.942C505.268 109.404 505.739 109.991 506.059 110.702C506.397 111.396 506.566 112.223 506.566 113.183C506.566 114.553 506.22 115.7 505.526 116.624C504.85 117.549 503.934 118.252 502.778 118.732C501.622 119.194 500.333 119.425 498.91 119.425ZM507.783 112.063C507.623 111.013 507.454 109.929 507.277 108.808C507.099 107.688 506.948 106.576 506.823 105.474C506.716 104.353 506.663 103.269 506.663 102.219V99.9518H512.372V102.219C512.372 103.269 512.309 104.353 512.185 105.474C512.078 106.576 511.936 107.688 511.758 108.808C511.58 109.929 511.411 111.013 511.251 112.063H507.783ZM509.544 119.452C508.708 119.452 508.059 119.221 507.597 118.758C507.134 118.296 506.903 117.638 506.903 116.784C506.903 116.002 507.143 115.362 507.623 114.864C508.121 114.366 508.762 114.117 509.544 114.117C510.38 114.117 511.029 114.348 511.491 114.81C511.954 115.273 512.185 115.931 512.185 116.784C512.185 117.567 511.936 118.207 511.438 118.705C510.958 119.203 510.327 119.452 509.544 119.452Z"
					fill="var(--color-logo-background)"
					initial={{ scale: 0, opacity: 0, y: -20 }}
					animate={{ scale: 1, opacity: 1, y: 0 }}
					transition={{
						type: "spring",
						stiffness: 500,
						damping: 30,
						duration: 1,
						delay: 2,
						fillOpacity: { duration: 0.6, delay: 2, ease: "easeInOut" },
					}}
				/>
			</g>
		</svg>
	);
};

export default LogoMinAnimated;
