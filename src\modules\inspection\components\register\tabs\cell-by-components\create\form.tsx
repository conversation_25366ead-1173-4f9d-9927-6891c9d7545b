import { <PERSON>, <PERSON> } from "lucide-react";
import { UseFormReturn } from "react-hook-form";
import { GenericSearchSelect } from "../../../../../../../shared/components/custom/generic-search-select";
import { <PERSON><PERSON> } from "../../../../../../../shared/components/shadcn/button";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "../../../../../../../shared/components/shadcn/form";
import { useFindAllCell } from "../../../../../../cell/hooks/list/find-all.hook";
import { useFindAllComponentTypes } from "../../../../../../component-type/hooks/list/find-all.hook";
import { TCreateCellComponentTypeForm } from "../../../../../validators/cell-components/create";
import { requiredLabel } from "../../forms/form-item/form";

interface ICreateCellComponentForm {
	methods: UseFormReturn<TCreateCellComponentTypeForm>;
	onSubmit: (data: TCreateCellComponentTypeForm) => void;
	onClose: () => void;
}

export const CreateInspectionCellComponentForm = ({ methods, onSubmit, onClose }: ICreateCellComponentForm) => {
	return (
		<Form {...methods}>
			<form onSubmit={methods.handleSubmit(onSubmit)} className="flex flex-col gap-4">
				<div className="space-y-5">
					<FormField
						control={methods.control}
						name="cell"
						render={({ field }) => (
							<FormItem>
								<FormLabel className="font-semibold">{requiredLabel("Célula")}</FormLabel>
								<FormControl className="w-full">
									<GenericSearchSelect
										value={field?.value}
										useDataHook={useFindAllCell}
										onChange={value => field.onChange(value)}
										displayField={item => item.name}
										placeholder="Selecione..."
										searchPlaceholder="Buscar célula..."
										loadingText="Carregando..."
										emptyText="Nenhuma célula encontrada."
										width="w-full"
									/>
								</FormControl>
								<FormMessage />
							</FormItem>
						)}
					/>
					<FormField
						control={methods.control}
						name="component"
						render={({ field }) => (
							<FormItem>
								<FormLabel className="font-semibold">{requiredLabel("Tipo de Componente")}</FormLabel>
								<FormControl className="w-full">
									<GenericSearchSelect
										value={field?.value}
										useDataHook={useFindAllComponentTypes}
										displayField={item => item.name}
										onChange={value => field.onChange(value)}
										placeholder="Selecione..."
										searchPlaceholder="Buscar tipo de componente..."
										loadingText="Carregando..."
										emptyText="Nenhum tipo de componente encontrado."
										width="w-full"
									/>
								</FormControl>
								<FormMessage />
							</FormItem>
						)}
					/>
				</div>
				<div className="flex justify-end gap-2 pt-4">
					<Button type="button" variant="outline" onClick={onClose}>
						<X className="mr-2" /> Cancelar
					</Button>
					<Button type="submit" className="bg-primary hover:bg-primary/30 px-6 py-2 text-white">
						<Link className="mr-2" />
						Vincular
					</Button>
				</div>
			</form>
		</Form>
	);
};
