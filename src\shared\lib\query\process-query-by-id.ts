import { IBaseHookFindByIdReturn } from "@/shared/types/hooks/hook-find-by-id.type";
import { ApiResponse } from "@/shared/types/requests/request.type";

export const processQueryById = <T>(data: ApiResponse<T> | undefined, isFetched: boolean): Omit<IBaseHookFindByIdReturn<T>, "isLoading"> => {
	const isNoDataFound = !data?.success && data?.status === 404;
	const hasPermissionError = !data?.success && data?.status === 403;

	return {
		data: data?.success ? data.data : null,
		hasError: isFetched && !data?.success && !isNoDataFound && !hasPermissionError,
		error: !data?.success && !hasPermissionError ? data?.data?.message : undefined,
		isEmpty: isNoDataFound,
	};
};
