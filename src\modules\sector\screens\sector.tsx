"use client";

import { useModal } from "@/shared/hooks/utils/modal.hook";
import { CreateSectorModal } from "../components/create/modal";
import { TableSector } from "../components/list/table";

export const SectorScreen = () => {
	const sectorModal = useModal();

	return (
		<main id="sector" className="flex h-full w-full flex-1 flex-col gap-6">
			<TableSector onOpenModal={sectorModal.toggleModal} />
			<CreateSectorModal isOpen={sectorModal.isOpen} onClose={sectorModal.toggleModal} />
		</main>
	);
};
