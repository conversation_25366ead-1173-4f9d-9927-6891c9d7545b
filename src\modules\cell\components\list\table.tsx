import { pathService } from "@/config/path-manager/service";
import { MainHeaderComponent } from "@/shared/components/custom/admin-header";
import { GenericEmptyState } from "@/shared/components/custom/empty";
import { ComponentType, useState } from "react";
import { CELL_SUBJECTS } from "../../constants/subjects";
import { useTableCell } from "../../hooks/list/cell-list.hook";
import { CellDesktopTableView } from "./desktop-table-view";
import { CellMobileTableView } from "./mobile-table-view";

interface ITableCellProps {
	onOpenModal: () => void;
}

export const TableCell = ({ onOpenModal }: ITableCellProps) => {
	const [searchTerm, setSearchTerm] = useState<string>("");
	const item = pathService.getItemById("cell");
	const Icon = item?.icon as ComponentType<unknown> | undefined;

	const { data, isLoading, pagination, isEmpty, isMobile, error, hasError, pageSize, handlePageSizeChange, setCurrentPage } = useTableCell({
		searchTerm,
	});

	return (
		<main className="container mx-auto flex h-full min-h-full flex-1 flex-col space-y-6 px-4 py-1 md:px-2">
			<MainHeaderComponent
				icon={Icon ? <Icon /> : undefined}
				title="Células"
				description="Cadastros de células de produção."
				total={pagination?.totalItems}
				search={searchTerm}
				onSearchChange={setSearchTerm}
				onAdd={onOpenModal}
				searchPlaceholder="Buscar células..."
				totalLabelSingular="célula"
				totalLabelPlural="células"
				subject={CELL_SUBJECTS.CELL}
			/>
			<div className="h-full">
				{isEmpty && !isLoading ? (
					<GenericEmptyState
						buttonText="Adicionar célula"
						description="Ainda não há células cadastradas."
						onAction={() => onOpenModal()}
						title="Nenhuma célula encontrada"
						subject={CELL_SUBJECTS.CELL}
					/>
				) : isMobile ? (
					<CellMobileTableView
						data={data}
						isLoading={isLoading}
						hasError={hasError}
						error={error}
						searchTerm={searchTerm}
						pagination={pagination ?? null}
						onPageChange={setCurrentPage}
						onPageSizeChange={handlePageSizeChange}
					/>
				) : (
					<CellDesktopTableView
						data={data}
						isLoading={isLoading}
						hasError={hasError}
						error={error}
						pagination={pagination ?? null}
						onPageChange={setCurrentPage}
						onPageSizeChange={handlePageSizeChange}
						pageSize={pageSize}
					/>
				)}
			</div>
		</main>
	);
};
