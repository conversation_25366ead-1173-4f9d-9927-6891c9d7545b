"use client";

import { processQueryResponsePaginated } from "@/shared/lib/query/process-query-response";
import { IBaseHookPaginatedReturn } from "@/shared/types/hooks/hook-paginated.type";
import { useQuery } from "@tanstack/react-query";
import { usePermissions } from "../../../../shared/hooks/permissions/permissions.hook";
import { createGetRequest } from "../../../../shared/lib/requests";
import { IPaginationParameters } from "../../../../shared/types/pagination/types";
import { IResponsePaginated } from "../../../../shared/types/requests/response-paginated.type";
import { COMPONENTS_TYPES_ENDPOINTS } from "../../api/endpoints";
import { componentsKeys } from "../../constants/query";
import { COMPONENT_TYPE_SUBJECTS } from "../../constants/subjects";
import { IComponentTypesDto } from "../../types/find-all.dto";

export const useFindAllComponentTypes = (params: IPaginationParameters): IBaseHookPaginatedReturn<IComponentTypesDto> => {
	const { canRead } = usePermissions();

	const { data, isLoading, isFetched } = useQuery({
		queryKey: componentsKeys.list({
			...params,
		}),
		queryFn: () => createGetRequest<IResponsePaginated<IComponentTypesDto>>(COMPONENTS_TYPES_ENDPOINTS.FIND_ALL(params)),
		enabled: canRead(COMPONENT_TYPE_SUBJECTS.COMPONENT_TYPE),
	});

	return {
		...processQueryResponsePaginated(data, isFetched),
		isLoading,
	};
};
