"use client";

import { BotIcon } from "lucide-react";
import { ComponentType, useState } from "react";
import { pathService } from "../../../../config/path-manager/service";
import { MainHeaderComponent } from "../../../../shared/components/custom/admin-header";
import { GenericEmptyState } from "../../../../shared/components/custom/empty";
import { PRODUCT_TYPE_SUBJECTS } from "../../constants/subjects";
import { useTableProductType } from "../../hooks/list/product-type-table.hook";
import { ProductTypeDesktopTableView } from "./desktop-table-view";
import { ProductTypeMobileTableView } from "./mobile-table-view";

interface ITableProductTypeProps {
	onOpenModal: () => void;
}

export const TableProductType = ({ onOpenModal }: ITableProductTypeProps) => {
	const [searchTerm, setSearchTerm] = useState("");
	const item = pathService.getItemById("product-type");
	const Icon = item?.icon as ComponentType<unknown> | undefined;
	const { data, isLoading, error, hasError, pagination, pageSize, isMobile, handlePageSizeChange, setCurrentPage, isEmpty } = useTableProductType({
		searchTerm,
	});

	const renderTable = () => {
		if (isMobile) {
			return (
				<>
					<ProductTypeMobileTableView
						data={data}
						isLoading={isLoading}
						hasError={hasError}
						error={error}
						searchTerm={searchTerm}
						pagination={pagination}
						onPageChange={setCurrentPage}
						onPageSizeChange={handlePageSizeChange}
					/>
				</>
			);
		} else {
			return (
				<>
					<ProductTypeDesktopTableView
						data={data}
						isLoading={isLoading}
						hasError={hasError}
						error={error}
						pageSize={pageSize}
						pagination={pagination}
						onPageChange={setCurrentPage}
						onPageSizeChange={handlePageSizeChange}
					/>
				</>
			);
		}
	};

	return (
		<main className="container mx-auto flex h-full min-h-full flex-1 flex-col space-y-6 px-4 py-1 md:px-2">
			<MainHeaderComponent
				icon={Icon ? <Icon /> : <BotIcon />}
				title="Tipos de produto"
				description="Gerencie o cadastro de tipos de produto"
				search={searchTerm}
				onSearchChange={setSearchTerm}
				total={pagination?.totalItems || 0}
				onAdd={() => onOpenModal()}
				subject={PRODUCT_TYPE_SUBJECTS.PRODUCT_TYPE}
			/>
			<div className="h-full">
				{isEmpty || (data.length === 0 && !isLoading) ? (
					<GenericEmptyState
						buttonText="Adicionar tipo de produto"
						description="Ainda não há tipos de produto cadastrados."
						onAction={() => onOpenModal()}
						title="Nenhum tipo de produto encontrado"
						subject={PRODUCT_TYPE_SUBJECTS.PRODUCT_TYPE}
					/>
				) : (
					renderTable()
				)}
			</div>
		</main>
	);
};
