import z from "zod";
import { Entity } from "../forms-links/create";

const requiredEntitySchema = (message: string) =>
	z.custom<Entity>(val => val && typeof val === "object" && typeof val.id === "number" && typeof val.name === "string", { message });

export const createCellByProductTypeSchema = z.object({
	cell: requiredEntitySchema("Selecione uma célula."),
	productType: requiredEntitySchema("Selecione um tipo de produto."),
});

export type TCreateCellByProductTypeForm = z.infer<typeof createCellByProductTypeSchema>;
