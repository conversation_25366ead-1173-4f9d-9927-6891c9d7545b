import { buildQueryParams } from "../../../shared/lib/utils/url-query-params";
import { IPaginationParameters } from "../../../shared/types/pagination/types";

const BASE = "/component-types";

export const COMPONENTS_TYPES_ENDPOINTS = Object.freeze({
	CREATE: BASE,
	FIND_ALL: (params?: IPaginationParameters) => buildQueryParams(BASE, { ...params }),
	DELETE: (id: number) => `${BASE}/${encodeURIComponent(id)}`,
} as const);
