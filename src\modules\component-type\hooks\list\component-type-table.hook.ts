"use client";

import { useIsMobile } from "@/shared/hooks/shadcn/use-mobile";
import { usePagination } from "@/shared/hooks/utils";
import { useFindAllComponentTypes } from "./find-all.hook";

interface UseComponentTypeProps {
	searchTerm: string;
}

export const useTableComponentType = ({ searchTerm }: UseComponentTypeProps) => {
	const { currentPage, setCurrentPage, pageSize, setItemsPerPage } = usePagination();
	const isMobile = useIsMobile();

	const { data, isLoading, error, pagination, hasError, isEmpty } = useFindAllComponentTypes({
		limit: pageSize,
		page: currentPage,
		search: searchTerm || "",
	});

	const handlePageSizeChange = (size: number) => {
		setItemsPerPage(size);
		setCurrentPage(1);
	};

	return {
		data,
		isLoading,
		isEmpty,
		error,
		hasError,
		pagination,
		pageSize,
		isMobile,
		handlePageSizeChange,
		setCurrentPage,
	};
};
