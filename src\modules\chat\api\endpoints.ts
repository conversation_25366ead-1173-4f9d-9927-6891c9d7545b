import { buildQueryParams } from "@/shared/lib/utils/url-query-params";
import { IPaginationParameters } from "@/shared/types/pagination/types";

const BASE = "/ai";

export const CHAT_ENDPOINTS = Object.freeze({
	ASK: `${BASE}-orchestrador/ask`,
	ADD_KNOWLEDGE: `${BASE}/knowledges`,
	FIND_ALL_KNOWLEDGE: (params?: IPaginationParameters) => buildQueryParams(`${BASE}/knowledges`, { ...params }),
	FIND_KNOWLEDGE_BY_ID: (id: string) => `${BASE}/knowledges/${encodeURIComponent(id)}`,
	REMOVE_KNOWLEDGE: (id: string) => `${BASE}/knowledges/${encodeURIComponent(id)}`,
	UPDATE_KNOWLEDGE: (id: string) => `${BASE}/knowledges/${encodeURIComponent(id)}`,
} as const);
