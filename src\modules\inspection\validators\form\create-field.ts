import z from "zod";
import { InspectionFormTypeEnum } from "../../constants/form/type-enum";

export const createFieldSchema = z
	.object({
		field: z
			.object({
				id: z.number().optional(),
				name: z.string().min(1, "Campo é obrigatório"),
			})
			.refine(data => typeof data?.id === "number" && data.id > 0 && data.name.length > 0, {
				message: "Campo é obrigatório",
			}),
		nickname: z.string().optional(),
		required: z.boolean().default(false),
		// group: z.number().optional(),
		sequence: z.number().optional(),
		typeId: z
			.nativeEnum(InspectionFormTypeEnum, {
				errorMap: () => ({ message: "Tipo de campo é obrigatório" }),
			})
			.optional()
			.refine(value => value === undefined || Object.values(InspectionFormTypeEnum).includes(value), {
				message: "Tipo de campo é obrigatório",
			}),
		measure: z
			.object({
				id: z.number().optional(),
				name: z.string().optional(),
			})
			.optional(),
		// groupTitle: z.string().optional(),
		biFilter: z.boolean().default(false),
		id: z.string().optional(),
		options: z
			.array(
				z.object({
					sequence: z.number().min(1, "Sequência da opção é obrigatória"),
					option: z.string().min(1, "Opção não pode estar vazia"),
					tempId: z.string(),
					id: z.number().optional(),
				}),
			)
			.optional(),
		tempId: z.string(),
	})
	.refine(
		data => {
			if (data.typeId === InspectionFormTypeEnum.OPTIONS) {
				return data.options && data.options.length > 0 && data.options.every(opt => opt.option.trim().length > 0);
			}
			return true;
		},
		{
			message: "Pelo menos uma opção é obrigatória para campos do tipo 'Opções'",
			path: ["options"],
		},
	);

export type ICreateFieldForm = z.infer<typeof createFieldSchema>;
