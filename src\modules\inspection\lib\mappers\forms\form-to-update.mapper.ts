import { IUpdateFormDTO } from "../../../types/forms/dtos/update-form.dto";
import { IFieldGroup } from "../../../types/forms/fields-table/fields-group.type";
import { ICreateForm } from "../../../validators/form/create";

export class InspectionFormToUpdateMapper {
	static map(data: ICreateForm, fields: IFieldGroup[]): IUpdateFormDTO {
		return {
			title: data.title,
			text: data.text,
			nomenclature: data.nomenclature,
			developerId: data.developer.id,
			approverId: data.approver.id,
			fields: fields.flatMap(field =>
				field.items.map(item => ({
					fieldId: item.field.id!,
					nickname: item.nickname ?? "",
					required: item.required ?? false,
					group: field.group ?? undefined,
					sequence: item.sequence ?? 1,
					typeId: Number(item.typeId),
					measureId: item.measure?.id ? Number(item.measure.id!) : undefined,
					groupTitle: field.groupTitle ?? undefined,
					biFilter: item.biFilter ?? false,
					id: Number(item.id) || undefined,
					options: (item.options ?? []).map((opt, optIdx) => ({
						sequence: opt.sequence ?? optIdx + 1,
						option: opt.option ?? "",
						id: opt.id ?? undefined,
					})),
				})),
			),
		};
	}
}
