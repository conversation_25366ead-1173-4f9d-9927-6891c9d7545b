import { ProtectedComponent } from "@/shared/components/auth/protected";
import { Badge } from "@/shared/components/shadcn/badge";
import { Button } from "@/shared/components/shadcn/button";
import { Card, CardContent } from "@/shared/components/shadcn/card";
import { Separator } from "@/shared/components/shadcn/separator";
import { useModal } from "@/shared/hooks/utils/modal.hook";
import { CheckCircle, Trash, XCircle } from "lucide-react";
import { IProductTypesDto } from "../../types/find-all.dto";
import { DeleteProductTypeModal } from "../delete/modal";

interface IProductTypeCardMobileProps {
	item: IProductTypesDto;
}

export const ProductTypeCardMobile = ({ item }: IProductTypeCardMobileProps) => {
	const modals = {
		edit: useModal(),
		delete: useModal(),
	};

	return (
		<Card className="bg-card relative border shadow-sm transition-shadow hover:shadow-md">
			<CardContent>
				<div className="mb-3 flex-1 items-start justify-between">
					<h3 className="text-card-foreground mr-2 line-clamp-2 flex-1 pb-3 text-base leading-tight font-semibold">{item.name}</h3>
					<div className="flex flex-col gap-2">
						<div className="flex w-full justify-between gap-1">
							<span className="text-muted-foreground text-s pr-1">Fábrica:</span>
							<Badge variant={item.factoryStatus ? "default" : "secondary"} className="flex items-center gap-1 text-xs">
								{item.factoryStatus ? <CheckCircle className="h-3 w-3" /> : <XCircle className="h-3 w-3" />}
								{item.factoryStatus ? "Ativo" : "Inativo"}
							</Badge>
						</div>
						<div className="flex w-full justify-between gap-1">
							<span className="text-muted-foreground text-s pr-1">Comercial:</span>
							<Badge variant={item.businessStatus ? "default" : "secondary"} className="flex items-center gap-1 text-xs">
								{item.businessStatus ? <CheckCircle className="h-3 w-3" /> : <XCircle className="h-3 w-3" />}
								{item.businessStatus ? "Ativo" : "Inativo"}
							</Badge>
						</div>
					</div>
				</div>

				<Separator className="my-4" />

				<div className="flex gap-2">
					<ProtectedComponent action="manage" subject="all">
						<Button
							size="sm"
							variant="ghost"
							className="border-destructive bg-destructive/10 text-destructive hover:bg-destructive/20 hover:text-destructive h-8 flex-1 border px-2 text-xs"
							onClick={modals.delete.toggleModal}
						>
							<Trash className="h-3 w-3" />
							<span>Excluir</span>
						</Button>
					</ProtectedComponent>
				</div>
			</CardContent>

			<DeleteProductTypeModal isOpen={modals.delete.isOpen} onClose={modals.delete.closeModal} name={item.name} id={item.id.toString()} />
		</Card>
	);
};
