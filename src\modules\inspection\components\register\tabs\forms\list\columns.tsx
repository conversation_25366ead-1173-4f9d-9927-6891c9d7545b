import { ColumnDef } from "@tanstack/react-table";
import { <PERSON> } from "lucide-react";
import { IFormDto } from "../../../../../types/forms/dtos/find-all.dto";
import { FormListActions } from "./actions";

export const inspectionFormColumns: ColumnDef<IFormDto>[] = [
	{
		accessorKey: "titulo",
		header: () => <div className="pl-[10px] text-start font-semibold">T<PERSON><PERSON><PERSON></div>,
		cell: ({ row }) => (
			<div className="flex items-center gap-2 pl-[10px] text-start">
				<span className="text-text-primary block max-w-[200px] truncate font-medium">{row.original.title}</span>
				{!row.original.canUpdate && (
					<div className="bg-primary/5 flex items-center gap-1 rounded-full px-2 py-0.5">
						<Link className="text-text-primary h-3 w-3" />
					</div>
				)}
			</div>
		),
	},
	{
		accessorKey: "nomenclatura",
		header: () => <div className="text-center font-semibold">Nomenclatura</div>,
		cell: ({ row }) => (
			<div className="flex justify-center">
				<span className="text-muted-foreground text-sm font-medium">{row.original.nomenclature}</span>
			</div>
		),
	},
	{
		accessorKey: "revisao",
		header: () => <div className="text-center font-semibold">Revisão</div>,
		cell: ({ row }) => (
			<div className="text-center">
				<span className="text-muted-foreground text-sm font-medium">{row.original.revision}</span>
			</div>
		),
	},
	{
		id: "actions",
		header: () => <div className="pr-[10px] text-right font-semibold">Ações</div>,
		cell: ({ row }) => <FormListActions formId={row.original.id} title={row.original.title} />,
		enableSorting: false,
		enableHiding: false,
		size: 80,
	},
];
