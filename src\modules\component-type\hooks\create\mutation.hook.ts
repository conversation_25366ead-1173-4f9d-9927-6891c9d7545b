import { toast } from "@/core/toast";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { usePermissions } from "../../../../shared/hooks/permissions/permissions.hook";
import { createPostRequest } from "../../../../shared/lib/requests";
import { IMessageGlobalReturn } from "../../../../shared/types/requests/message.type";
import { COMPONENTS_TYPES_ENDPOINTS } from "../../api/endpoints";
import { componentsKeys } from "../../constants/query";
import { COMPONENT_TYPE_SUBJECTS } from "../../constants/subjects";
import { TCreateComponentType } from "../../validators/create.validator";

export const useCreateComponentTypeMutation = (onClose: () => void) => {
	const queryClient = useQueryClient();
	const { canCreate } = usePermissions();

	const mutation = useMutation({
		mutationKey: componentsKeys.custom("create"),
		mutationFn: async (componentType: TCreateComponentType) => {
			if (!canCreate(COMPONENT_TYPE_SUBJECTS.COMPONENT_TYPE)) throw new Error("Você não tem permissão para criar este tipo de componente.");
			const { data, success } = await createPostRequest<IMessageGlobalReturn>(COMPONENTS_TYPES_ENDPOINTS.CREATE, componentType);
			if (!success) throw new Error(data.message);
			return data;
		},
		onSuccess: () => {
			componentsKeys.invalidateAll(queryClient);
			onClose();
		},
	});
	return {
		createComponentType: (form: TCreateComponentType) =>
			toast.promise(mutation.mutateAsync(form), {
				loading: "Criando tipo de componente...",
				success: ({ message }) => message,
				error: ({ message }) => message,
			}),
		...mutation,
	};
};
