import { EmptyStateTable } from "@/shared/components/custom/empty/table-empty";
import { Pagination } from "@/shared/components/custom/pagination";
import { Badge } from "@/shared/components/shadcn/badge";
import { Separator } from "@/shared/components/shadcn/separator";
import { Skeleton } from "@/shared/components/shadcn/skeleton";
import { ComponentIcon, List, Search } from "lucide-react";
import { IProductTypesDto } from "../../types/find-all.dto";
import { ProductTypeCardMobile } from "./card-mobile";

interface MobileTableViewProps {
	data: IProductTypesDto[] | undefined;
	isLoading: boolean;
	hasError: boolean;
	error: string | false | undefined;
	searchTerm: string;
	pagination: {
		currentPage: number;
		totalPages: number;
		itemsPerPage: number;
		totalItems: number;
	} | null;
	onPageChange: (page: number) => void;
	onPageSizeChange: (size: number) => void;
}

export const ProductTypeMobileTableView = ({
	data,
	isLoading,
	hasError,
	error,
	searchTerm,
	pagination,
	onPageChange,
	onPageSizeChange,
}: MobileTableViewProps) => {
	const renderItemsHeader = () => {
		if (hasError || isLoading || !data || data.length === 0) return null;

		const totalItems = pagination?.totalItems || data.length;
		const currentPageItems = data.length;
		const currentPage = pagination?.currentPage || 1;
		const totalPages = pagination?.totalPages || 1;

		return (
			<div className="mb-4 space-y-3">
				<div className="flex items-center gap-3">
					<List className="text-primary h-5 w-5" />
					<div className="flex items-center gap-2">
						<h3 className="text-foreground text-sm font-medium">Lista de Tipos de Produto</h3>
						<Badge variant="secondary" className="text-xs">
							{totalItems} {totalItems === 1 ? "item" : "itens"}
						</Badge>
					</div>
				</div>

				{searchTerm && (
					<div className="text-muted-foreground flex items-center gap-2 text-xs">
						<Search className="h-3 w-3" />
						<span>Filtrado por: &ldquo;{searchTerm}&rdquo;</span>
					</div>
				)}

				{pagination && totalPages > 1 && (
					<div className="text-muted-foreground text-xs">
						Página {currentPage} de {totalPages} • Exibindo {currentPageItems} de {totalItems} tipos de produto
					</div>
				)}

				<Separator className="my-2" />
			</div>
		);
	};
	const renderContent = () => {
		if (hasError) {
			return <div className="text-destructive h-24 text-center">Erro ao carregar os dados: {error || "Ocorreu um erro desconhecido."}</div>;
		}

		if (isLoading) return <Skeleton className="mx-auto h-6 w-48" />;
		if (data && data.length) return data.map(item => <ProductTypeCardMobile key={item.id} item={item} />);

		return (
			<EmptyStateTable
				searchTerm={searchTerm}
				icon={<ComponentIcon />}
				title="Nenhum tipo de produto encontrado"
				description={searchTerm ? "Nenhum tipo de produto corresponde ao termo pesquisado." : "Ainda não há tipos de produtos cadastrados."}
				tip="Você pode tentar pesquisar por outros termos ou adicionar um novo tipo de produto."
			/>
		);
	};

	return (
		<div className="space-y-4">
			{renderItemsHeader()}
			{renderContent()}
			{pagination && (
				<Pagination
					currentPage={pagination.currentPage}
					totalPages={pagination.totalPages}
					pageSize={pagination.itemsPerPage}
					totalItems={pagination.totalItems}
					onPageChange={onPageChange}
					onPageSizeChange={size => {
						onPageSizeChange(size);
						onPageChange(1);
					}}
					showPageSizeSelector
					showSelectedInfo
				/>
			)}
		</div>
	);
};
