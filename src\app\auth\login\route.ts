import { extractLoginParams, fetchBackendLoginUrl, isRedirectResponse, isValidRedirectPath } from "@/core/auth/lib/login-utils";
import { NextRequest, NextResponse } from "next/server";

export async function GET(request: NextRequest): Promise<NextResponse> {
	try {
		const { redirectPath, theme } = await extractLoginParams(new URL(request.url));
		if (!isValidRedirectPath(redirectPath)) {
			const errorUrl = new URL("/auth/error", request.url);
			errorUrl.searchParams.set("error", "invalid_redirect");
			errorUrl.searchParams.set("message", "Path de redirecionamento inválido");
			errorUrl.searchParams.set("redirect", redirectPath);
			return NextResponse.redirect(errorUrl, 302);
		}

		const backendResponse = await fetchBackendLoginUrl(redirectPath, theme);
		if (isRedirectResponse(backendResponse)) {
			const location = backendResponse.location;
			if (!location) throw new Error("URL de redirecionamento não encontrada");
			return NextResponse.redirect(location, 302);
		}

		const errorUrl = new URL("/auth/error", request.url);
		let errorType = "backend_error";
		if (backendResponse.status === 0) {
			const errorText = backendResponse.errorText || "";
			if (errorText.includes("timeout") || errorText.includes("Tempo limite")) {
				errorType = "timeout_error";
			} else {
				errorType = "network_error";
			}
		}
		errorUrl.searchParams.set("error", errorType);
		errorUrl.searchParams.set("message", backendResponse.errorText || "Erro na comunicação com o servidor de autenticação");
		errorUrl.searchParams.set("status", backendResponse.status.toString());
		errorUrl.searchParams.set("redirect", redirectPath);
		return NextResponse.redirect(errorUrl, 302);
	} catch (error) {
		console.error("Erro no redirecionamento para Keycloak:", error);
		const errorUrl = new URL("/auth/error", request.url);
		errorUrl.searchParams.set("error", "keycloak_error");
		errorUrl.searchParams.set("message", "Erro interno ao processar redirecionamento para autenticação");
		const { redirectPath: fallbackRedirectPath } = await extractLoginParams(new URL(request.url));
		errorUrl.searchParams.set("redirect", fallbackRedirectPath);
		return NextResponse.redirect(errorUrl, 302);
	}
}
