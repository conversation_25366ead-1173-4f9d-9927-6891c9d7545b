"use client";

import { useIsMobile } from "@/shared/hooks/shadcn/use-mobile";
import { usePagination } from "@/shared/hooks/utils";
import { useFindAllProductTypes } from "./find-all.hook";

interface UseProductTypeProps {
	searchTerm: string;
}

export const useTableProductType = ({ searchTerm }: UseProductTypeProps) => {
	const { currentPage, setCurrentPage, pageSize, setItemsPerPage } = usePagination();
	const isMobile = useIsMobile();

	const { data, isLoading, error, pagination, hasError, isEmpty } = useFindAllProductTypes({
		limit: pageSize,
		page: currentPage,
		search: searchTerm || "",
	});

	const handlePageSizeChange = (size: number) => {
		setItemsPerPage(size);
		setCurrentPage(1);
	};

	return {
		data,
		isLoading,
		isEmpty,
		error,
		hasError,
		pagination,
		pageSize,
		isMobile,
		handlePageSizeChange,
		setCurrentPage,
	};
};
