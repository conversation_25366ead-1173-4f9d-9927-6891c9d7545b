"use client";

import { useModal } from "../../../shared/hooks/utils/modal.hook";
import { CreateComponentTypeModal } from "../components/create/modal";
import { TableComponentType } from "../components/table/table";

export const ComponentTypeScreen = () => {
	const addComponentType = useModal();

	return (
		<main id="component-type" className="flex h-full w-full flex-1 flex-col gap-6">
			<TableComponentType onOpenModal={addComponentType.toggleModal} />
			<CreateComponentTypeModal isOpen={addComponentType.isOpen} onClose={addComponentType.toggleModal} />
		</main>
	);
};
