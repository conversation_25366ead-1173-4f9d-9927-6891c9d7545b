import { ACTIVITY_SUBJECTS_LIST } from "@/modules/activity/constants/subjects";
import { CELL_SUBJECTS_LIST } from "@/modules/cell/constants/subjects";
import { INSPECTION_SUBJECTS_LIST } from "@/modules/inspection/constants/permissions/subjects";
import { SECTOR_SUBJECTS_LIST } from "@/modules/sector/constants/subjects";
import {
	BotIcon,
	Box,
	ChartNoAxesCombinedIcon,
	HomeIcon,
	Ruler,
	RulerDimensionLine,
	ScrollText,
	SearchIcon,
	ShoppingBag,
	SlidersHorizontalIcon,
	SlidersVertical,
} from "lucide-react";
import { COMPONENT_TYPE_SUBJECTS_LIST } from "../../modules/component-type/constants/subjects";
import { PRODUCT_TYPE_SUBJECTS_LIST } from "../../modules/product-type/constants/subjects";
import { PATHS_CONFIG } from "./config";
import { ICreateItemGroupPathManager } from "./types";

export const pathItems: ICreateItemGroupPathManager[] = [
	{
		title: "Dashboard",
		items: [
			{
				id: "home",
				name: "Tela Inicial",
				description: "Bem-vindo ao S.I.M.P - Sistema Integrado de Medição e Manufatura Pormade",
				requiredSubjects: [],
				visibleOnMobile: true,
				visibleOnMenu: true,
				route: PATHS_CONFIG.DASHBOARD,
				icon: HomeIcon,
			},
		],
	},
	{
		title: "Medição",
		items: [
			{
				name: "Pré-medição",
				id: "pre-measurement",
				description: "Gerencie as pré-medições",
				requiredSubjects: ["all"],
				visibleOnMobile: true,
				visibleOnMenu: true,
				route: PATHS_CONFIG.MEDICAO.subPaths.PRE_MEDICAO,
				icon: RulerDimensionLine,
			},
			{
				name: "Planilha de Medição",
				id: "measurement-sheet",
				description: "Gerencie as medições",
				requiredSubjects: ["all"],
				visibleOnMobile: true,
				visibleOnMenu: true,
				route: PATHS_CONFIG.MEDICAO.subPaths.PLANILHA_MEDICAO,
				icon: Ruler,
			},
		],
	},
	{
		title: "Ciclo do Pedido",
		items: [
			{
				name: "Pedidos",
				id: "orders",
				description: "Gerencie os pedidos",
				requiredSubjects: ["all"],
				visibleOnMobile: true,
				visibleOnMenu: true,
				route: PATHS_CONFIG.CICLO_PEDIDO.subPaths.PEDIDOS,
				icon: ShoppingBag,
			},
			{
				name: "Lotes",
				id: "lots",
				description: "Gerencie os lotes",
				requiredSubjects: ["all"],
				visibleOnMobile: true,
				visibleOnMenu: true,
				route: PATHS_CONFIG.CICLO_PEDIDO.subPaths.LOTES,
				icon: Box,
			},
			{
				name: "Resumo",
				id: "summary-lot",
				description: "Resumo do ciclo do pedido",
				requiredSubjects: ["all"],
				visibleOnMobile: true,
				visibleOnMenu: true,
				route: PATHS_CONFIG.CICLO_PEDIDO.subPaths.RESUMO,
				icon: ScrollText,
			},
		],
	},
	{
		title: "Produção",
		items: [
			{
				name: "Cadastros",
				id: "production-sectors",
				description: "Gerencie os setores de produção",
				requiredSubjects: ["all"],
				visibleOnMobile: true,
				visibleOnMenu: true,
				route: PATHS_CONFIG.PRODUCAO.subPaths.CADASTROS,
				icon: SlidersVertical,
				subItems: [
					{
						name: "Apontamentos",
						id: "apontamentos",
						description: "Gerencie os apontamentos de produção",
						requiredSubjects: ["all"],
						visibleOnMobile: true,
						visibleOnMenu: true,
						route: PATHS_CONFIG.PRODUCAO.subPaths.CADASTROS.subPaths.APONTAMENTOS,
						icon: SlidersHorizontalIcon,
					},
					{
						name: "Atividades",
						id: "activity",
						description: "Gerencie as atividades de produção",
						requiredSubjects: [...ACTIVITY_SUBJECTS_LIST],
						visibleOnMobile: true,
						visibleOnMenu: true,
						route: PATHS_CONFIG.PRODUCAO.subPaths.CADASTROS.subPaths.ATIVIDADE,
						icon: SlidersHorizontalIcon,
					},
					{
						name: "Células",
						id: "cells",
						description: "Gerencie as células de produção",
						requiredSubjects: [...CELL_SUBJECTS_LIST],
						visibleOnMobile: true,
						visibleOnMenu: true,
						route: PATHS_CONFIG.PRODUCAO.subPaths.CADASTROS.subPaths.CELULAS,
						icon: SlidersHorizontalIcon,
					},
					{
						name: "Estoque",
						id: "inventory",
						description: "Gerencie o controle de estoque",
						requiredSubjects: ["all"],
						visibleOnMobile: true,
						visibleOnMenu: true,
						route: PATHS_CONFIG.PRODUCAO.subPaths.CADASTROS.subPaths.ESTOQUE,
						icon: SlidersHorizontalIcon,
					},
					{
						name: "Roteiros",
						id: "routes",
						description: "Gerencie os roteiros de produção",
						requiredSubjects: ["all"],
						route: PATHS_CONFIG.PRODUCAO.subPaths.CADASTROS.subPaths.ROTEIROS,
						visibleOnMobile: true,
						visibleOnMenu: true,
						icon: SlidersHorizontalIcon,
					},
					{
						name: "Setores",
						id: "sectors",
						description: "Gerencie os setores de produção",
						requiredSubjects: [...SECTOR_SUBJECTS_LIST],
						route: PATHS_CONFIG.PRODUCAO.subPaths.CADASTROS.subPaths.SETORES,
						visibleOnMobile: true,
						visibleOnMenu: true,
						icon: SlidersHorizontalIcon,
					},
					{
						name: "Tipo de produto",
						id: "product-type",
						description: "Gerencie os tipos de produto",
						requiredSubjects: [...PRODUCT_TYPE_SUBJECTS_LIST],
						visibleOnMobile: true,
						visibleOnMenu: true,
						route: PATHS_CONFIG.PRODUCAO.subPaths.CADASTROS.subPaths.TIPO_PRODUTO,
						icon: SlidersHorizontalIcon,
					},
					{
						name: "Tipo de componente",
						id: "component-type",
						description: "Gerencie os tipos de componente",
						requiredSubjects: [...COMPONENT_TYPE_SUBJECTS_LIST],
						visibleOnMobile: true,
						visibleOnMenu: true,
						route: PATHS_CONFIG.PRODUCAO.subPaths.CADASTROS.subPaths.TIPO_COMPONENTE,
						icon: SlidersHorizontalIcon,
					},
				],
			},
			{
				name: "Acompanhamento",
				id: "tracking",
				description: "Acompanhe o progresso da produção",
				requiredSubjects: ["all"],
				visibleOnMobile: true,
				visibleOnMenu: true,
				route: PATHS_CONFIG.PRODUCAO.subPaths.ACOMPANHAMENTO,
				icon: SearchIcon,
			},
			{
				name: "Estatísticas",
				id: "statistics",
				description: "Visualize estatísticas de produção",
				requiredSubjects: ["all"],
				visibleOnMobile: true,
				visibleOnMenu: true,
				route: PATHS_CONFIG.PRODUCAO.subPaths.ESTATISTICAS,
				icon: ChartNoAxesCombinedIcon,
			},
		],
	},
	{
		title: "Inspeção",
		items: [
			{
				id: "inspection-register",
				name: "Cadastros",
				description: "Defina formulários, medidas, campos e células de inspeção",
				requiredSubjects: [...INSPECTION_SUBJECTS_LIST],
				visibleOnMobile: true,
				visibleOnMenu: true,
				route: PATHS_CONFIG.INSPECTION.subPaths.REGISTER,
				icon: SlidersHorizontalIcon,
			},
		],
	},
	{
		title: "Chat",
		items: [
			{
				id: "chat",
				name: "Conhecimento",
				description: "Gerencie as conversas do chat",
				requiredSubjects: ["all"],
				visibleOnMobile: true,
				visibleOnMenu: true,
				route: PATHS_CONFIG.CHAT_ADMIN,
				icon: BotIcon,
			},
		],
	},
];
