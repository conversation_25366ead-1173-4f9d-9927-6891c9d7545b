import { pathService } from "@/config/path-manager/service";
import { MainHeaderComponent } from "@/shared/components/custom/admin-header";
import { GenericEmptyState } from "@/shared/components/custom/empty";
import { ComponentType, useState } from "react";
import { SECTOR_SUBJECTS } from "../../constants/subjects";
import { useTableSector } from "../../hooks/list/sector-list.hook";
import { SectorDesktopTableView } from "./desktop-table-view";
import { SectorMobileTableView } from "./mobile-table-view";

interface ITableSectorProps {
	onOpenModal: () => void;
}

export const TableSector = ({ onOpenModal }: ITableSectorProps) => {
	const [searchTerm, setSearchTerm] = useState<string>("");
	const item = pathService.getItemById("sector");
	const Icon = item?.icon as ComponentType<unknown> | undefined;

	const { data, isLoading, pagination, isEmpty, isMobile, error, hasError, pageSize, handlePageSizeChange, setCurrentPage } = useTableSector({
		searchTerm,
	});

	return (
		<main className="container mx-auto flex h-full min-h-full flex-1 flex-col space-y-6 px-4 py-1 md:px-2">
			<MainHeaderComponent
				icon={Icon ? <Icon /> : undefined}
				title="Setores"
				description="Cadastros de setores."
				total={pagination?.totalItems}
				search={searchTerm}
				onSearchChange={setSearchTerm}
				onAdd={onOpenModal}
				searchPlaceholder="Buscar setores..."
				totalLabelSingular="setor"
				totalLabelPlural="setors"
				subject={SECTOR_SUBJECTS.SECTOR}
			/>
			<div className="h-full">
				{isEmpty && !isLoading ? (
					<GenericEmptyState
						buttonText="Adicionar setor"
						description="Ainda não há setores cadastradas."
						onAction={() => onOpenModal()}
						title="Nenhum setor encontrado"
						subject={SECTOR_SUBJECTS.SECTOR}
					/>
				) : isMobile ? (
					<SectorMobileTableView
						data={data}
						isLoading={isLoading}
						hasError={hasError}
						error={error}
						searchTerm={searchTerm}
						pagination={pagination ?? null}
						onPageChange={setCurrentPage}
						onPageSizeChange={handlePageSizeChange}
					/>
				) : (
					<SectorDesktopTableView
						data={data}
						isLoading={isLoading}
						hasError={hasError}
						error={error}
						pagination={pagination ?? null}
						onPageChange={setCurrentPage}
						onPageSizeChange={handlePageSizeChange}
						pageSize={pageSize}
					/>
				)}
			</div>
		</main>
	);
};
