"use client";

import { useModal } from "../../../shared/hooks/utils/modal.hook";
import { CreateProductTypeModal } from "../components/create/modal";
import { TableProductType } from "../components/table/table";

export const ProductTypeScreen = () => {
	const addProductType = useModal();

	return (
		<main id="product-type" className="flex h-full w-full flex-1 flex-col gap-6">
			<TableProductType onOpenModal={addProductType.toggleModal} />
			<CreateProductTypeModal isOpen={addProductType.isOpen} onClose={addProductType.toggleModal} />
		</main>
	);
};
