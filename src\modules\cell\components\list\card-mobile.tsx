import { ProtectedComponent } from "@/shared/components/auth/protected";
import { Badge } from "@/shared/components/shadcn/badge";
import { Button } from "@/shared/components/shadcn/button";
import { Card, CardContent, CardHeader } from "@/shared/components/shadcn/card";
import { Separator } from "@/shared/components/shadcn/separator";
import { useModal } from "@/shared/hooks/utils/modal.hook";
import { MonitorSmartphone, Trash } from "lucide-react";
import { ICellDto } from "../../types/find-all.dto";
import { DeleteCellModal } from "../delete/modal";
interface ICellCardMobileProps {
	item: ICellDto;
}

export const CellCardMobile = ({ item }: ICellCardMobileProps) => {
	const modals = {
		delete: useModal(),
	};

	return (
		<Card className="bg-card relative border shadow-sm transition-shadow hover:shadow-md">
			<CardHeader className="pb-3">
				<div className="flex items-center gap-2">
					<MonitorSmartphone className="text-primary h-4 w-4" />
					<Badge variant="secondary" className="text-xs">
						Célula
					</Badge>
				</div>
			</CardHeader>
			<CardContent className="pt-0">
				<div className="mb-3 flex items-start justify-between">
					<h3 className="mr-2 line-clamp-2 flex-1 text-base leading-tight font-semibold">{item.name}</h3>
					<div className="bg-primary h-1.5 w-1.5 rounded-full opacity-60" />
				</div>
				<Separator className="my-4" />
				<div className="flex justify-end">
					<ProtectedComponent action="manage" subject="all">
						<Button
							size="sm"
							variant="ghost"
							className="border-destructive bg-destructive/10 text-destructive hover:bg-destructive/20 hover:text-destructive h-8 px-3 text-sm"
							onClick={modals.delete.toggleModal}
						>
							<Trash className="mr-1 h-4 w-4" />
							<span>Excluir</span>
						</Button>
					</ProtectedComponent>
				</div>
			</CardContent>

			<DeleteCellModal isOpen={modals.delete.isOpen} onClose={modals.delete.closeModal} name={item.name} id={String(item.id)} />
		</Card>
	);
};
