import { ColumnDef } from "@tanstack/react-table";
import { ICellDto } from "../../types/find-all.dto";
import { CellActions } from "./actions";

export const cellColumns: ColumnDef<ICellDto>[] = [
	{
		accessorKey: "name",
		header: () => <div className="pl-[10px] text-start font-semibold">Nome</div>,
		cell: ({ row }) => (
			<div className="flex items-center justify-start pl-[10px]">
				<div className="max-w-[300px]">
					<span className="text-text-primary block truncate font-medium" title={row.original.name}>
						{row.original.name}
					</span>
				</div>
			</div>
		),
		size: 300,
	},
	{
		id: "actions",
		header: () => <div className="pr-[10px] text-right font-semibold">Ações</div>,
		cell: ({ row }) => <CellActions cellId={row.original.id} name={row.original.name} />,
		size: 80,
	},
];
