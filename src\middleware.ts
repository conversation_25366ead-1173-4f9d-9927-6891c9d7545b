import { NextRequest, NextResponse } from "next/server";
import { UnifiedAuthMiddleware } from "./config/middleware/auth/unified-auth.middleware";
import { SecurityHeadersMiddleware } from "./config/middleware/security/security-headers.middleware";

function generateBase64Nonce(length = 16): string {
	const bytes = crypto.getRandomValues(new Uint8Array(length));
	let binary = "";
	for (const b of bytes) binary += String.fromCharCode(b);
	return btoa(binary).replace(/\+/g, "-").replace(/\//g, "_").replace(/=+$/, "");
}

export async function middleware(request: NextRequest): Promise<Response> {
	try {
		const nonce = generateBase64Nonce();
		const authResponse = await UnifiedAuthMiddleware.process(request);
		if (authResponse.status >= 300 && authResponse.status < 400) return SecurityHeadersMiddleware.addToResponse(authResponse, nonce);
		const next = NextResponse.next({
			request: {
				headers: new Headers({
					...Object.fromEntries(request.headers),
					"x-nonce": nonce,
				}),
			},
		});
		next.headers.set("x-nonce", nonce);
		return SecurityHeadersMiddleware.addToResponse(next, nonce);
	} catch (error) {
		console.error("Erro no middleware principal:", error);
		const fallbackNonce = generateBase64Nonce();
		const errorResponse = NextResponse.redirect(new URL("/auth/login", request.url), { status: 302 });
		return SecurityHeadersMiddleware.addToResponse(errorResponse, fallbackNonce);
	}
}

export const config = {
	matcher: ["/((?!api|_next/static|_next/image|_next/font|favicon.ico|events/devtools/events|events$|\\.well-known).*)"],
};
