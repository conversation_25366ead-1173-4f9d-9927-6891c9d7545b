"use client";

import { useModal } from "@/shared/hooks/utils/modal.hook";
import { CreateActivityModal } from "../components/create/modal";
import { TableActivity } from "../components/list/table";

export const ActivityScreen = () => {
	const activityModal = useModal();

	return (
		<main id="activity" className="flex h-full w-full flex-1 flex-col gap-6">
			<TableActivity onOpenModal={activityModal.toggleModal} />
			<CreateActivityModal isOpen={activityModal.isOpen} onClose={activityModal.toggleModal} />
		</main>
	);
};
