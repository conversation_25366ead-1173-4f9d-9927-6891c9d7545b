"use client";

import { useModal } from "@/shared/hooks/utils/modal.hook";
import { AddKnowledgeModal } from "../components/admin/create/add-knowledge-modal";
import { TableChatAdmin } from "../components/admin/table/table";

export const ChatAdminScreen = () => {
	const addKnowledgeModal = useModal();

	return (
		<main id="chat-admin" className="flex h-full w-full flex-1 flex-col gap-6">
			<TableChatAdmin onOpenModal={addKnowledgeModal.toggleModal} />
			<AddKnowledgeModal isOpen={addKnowledgeModal.isOpen} onClose={addKnowledgeModal.toggleModal} />
		</main>
	);
};
