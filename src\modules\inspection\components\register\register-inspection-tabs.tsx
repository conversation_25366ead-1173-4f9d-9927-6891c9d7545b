"use client";
import { ProtectedComponent } from "@/shared/components/auth/protected";
import { MainHeaderComponent } from "@/shared/components/custom/admin-header";
import { Tabs, TabsContent } from "@/shared/components/shadcn/tabs";
import { useModal } from "@/shared/hooks/utils/modal.hook";
import { Circle, Component, FileText, Grid, Link, Ruler, Users } from "lucide-react";
import { useState } from "react";
import { INSPECTION_SUBJECTS } from "../../constants/permissions/subjects";
import { TInspectionTabValue, useInspectionTabs } from "../../hooks/tabs/inspection-tabs.hook";
import { ITabItemRegisterInspectionTabs, RegisterInspectionHeader } from "./header/register-inspection-header";
import { ModalCreateCellComponent } from "./tabs/cell-by-components/create/modal";
import { CellComponentsTable } from "./tabs/cell-by-components/list/table";
import { ModalCreateCellByProductType } from "./tabs/cell-by-product-type/create/modal";
import { CellByProductTypeTable } from "./tabs/cell-by-product-type/list/table";
import ModalCreateCollabSector from "./tabs/collaborator-by-sector/create/modal";
import { CollabBySectorTable } from "./tabs/collaborator-by-sector/list/table";
import ModalCreateFields from "./tabs/fields/create/modal";
import { FieldsTable } from "./tabs/fields/list/table";
import { ModalCreateFormLink } from "./tabs/forms-links/create/modal";
import { FormsLinksTable } from "./tabs/forms-links/list/table";
import { ModalCreateForm } from "./tabs/forms/create/modal";
import { FormTable } from "./tabs/forms/list/table";
import ModalCreateMeasures from "./tabs/measures/create/modal";
import { MeasuresTable } from "./tabs/measures/list/table";

export const RegisterInspectionTabs = () => {
	const { activeTab, setActiveTab, availableTabs } = useInspectionTabs();
	const [searchTerm, setSearchTerm] = useState("");
	const inspectionModals = {
		measures: useModal(),
		fields: useModal(),
		forms: useModal(),
		components: useModal(),
		collaborators: useModal(),
		formLinks: useModal(),
		cellByProductType: useModal(),
	};

	const TAB_CONFIG: ITabItemRegisterInspectionTabs[] = [
		{
			value: "medidas",
			requiredPermission: INSPECTION_SUBJECTS.INSPECTION_MEASURE,
			label: "Medidas",
			icon: Ruler,
			renderContent: searchTerm => <MeasuresTable searchTerm={searchTerm} onNew={inspectionModals.measures.openModal} />,
			onNew: () => inspectionModals.measures.openModal(),
		},
		{
			value: "campos",
			label: "Campos",
			requiredPermission: INSPECTION_SUBJECTS.INSPECTION_FIELDS,
			icon: Grid,
			renderContent: searchTerm => <FieldsTable searchTerm={searchTerm} onNew={inspectionModals.fields.openModal} />,
			onNew: () => inspectionModals.fields.openModal(),
		},
		{
			value: "vinculo-colaboradores",
			label: "Vínculo de colaboradores",
			requiredPermission: INSPECTION_SUBJECTS.INSPECTION_COLLABORATOR_BY_SECTOR,
			icon: Users,
			renderContent: searchTerm => <CollabBySectorTable searchTerm={searchTerm} onNew={inspectionModals.collaborators.openModal} />,
			onNew: () => inspectionModals.collaborators.openModal(),
		},
		{
			value: "formularios",
			label: "Formulários",
			requiredPermission: INSPECTION_SUBJECTS.INSPECTION_FORM,
			icon: FileText,
			renderContent: searchTerm => <FormTable searchTerm={searchTerm} onNew={inspectionModals.forms.openModal} />,
			onNew: () => inspectionModals.forms.openModal(),
		},
		{
			value: "vinculos",
			label: "Vínculos",
			requiredPermission: INSPECTION_SUBJECTS.INSPECTION_FORM,
			icon: Link,
			renderContent: searchTerm => <FormsLinksTable searchTerm={searchTerm} onNew={inspectionModals.formLinks.openModal} />,
			onNew: () => inspectionModals.formLinks.openModal(),
		},
		{
			value: "tipo-produto",
			label: " Célula por tipo de produto",
			requiredPermission: INSPECTION_SUBJECTS.INSPECTION_CELL_PRODUCTION_BY_PRODUCT_TYPE,
			icon: Circle,
			renderContent: searchTerm => <CellByProductTypeTable searchTerm={searchTerm} onNew={inspectionModals.cellByProductType.openModal} />,
			onNew: () => inspectionModals.cellByProductType.openModal(),
		},
		{
			
			value: "componentes",
			label: "Célula por componente",
			requiredPermission: INSPECTION_SUBJECTS.INSPECTION_CELL_PRODUCTION_BY_COMPONENT,
			icon: Component,
			renderContent: searchTerm => <CellComponentsTable searchTerm={searchTerm} onNew={inspectionModals.components.openModal} />,
			onNew: () => inspectionModals.components.openModal(),
		},
	];

	const handleTabChange = (value: string) => {
		if (availableTabs.includes(value as TInspectionTabValue)) setActiveTab(value as TInspectionTabValue);
		setSearchTerm("");
	};

	const activeTabItem = TAB_CONFIG.find(tab => tab.value === activeTab);

	const getAddButtonText = () => {
		if (!activeTabItem) return "Adicionar";
		const specialTabs = ["vinculo-colaboradores", "tipo-produto", "componentes", "vinculos"];
		if (specialTabs.includes(activeTabItem.value)) {
			return "Vincular";
		}
		return "Adicionar";
	};

	return (
		<div className="flex h-full w-full flex-1 flex-col gap-4">
			<MainHeaderComponent
				subject={activeTabItem?.requiredPermission}
				icon={activeTabItem?.icon ? <activeTabItem.icon /> : undefined}
				title={activeTabItem?.label || "Cadastros de inspeção"}
				description={activeTabItem ? `Gerencie os ${activeTabItem.label.toLowerCase()} de inspeção` : "Gerencie os cadastros de inspeção"}
				search={searchTerm}
				onSearchChange={setSearchTerm}
				onAdd={() => activeTabItem?.onNew(searchTerm)}
				addButtonText={getAddButtonText()}
				searchPlaceholder={`Buscar ${activeTabItem?.label.toLowerCase() || "item"}`}
			/>

			<Tabs value={activeTab} onValueChange={handleTabChange} className="relative flex h-auto w-full flex-1 flex-col">
				<section id="tabs-header" className="main-h-0 w-full flex-none">
					<RegisterInspectionHeader
						tabItems={TAB_CONFIG}
						activeTab={activeTab}
						setActiveTab={setActiveTab}
						searchTerm={searchTerm}
						setSearchTerm={setSearchTerm}
						onNew={searchTerm => activeTabItem?.onNew(searchTerm)}
					/>
				</section>
				<section id="tabs-content" className="flex flex-1 overflow-x-hidden">
					{TAB_CONFIG.map(({ value, renderContent, requiredPermission }) => (
						<ProtectedComponent action="read" subject={requiredPermission} key={value}>
							<TabsContent value={value} className="mt-0 flex h-full flex-1 flex-col">
								{renderContent(searchTerm)}
							</TabsContent>
						</ProtectedComponent>
					))}
				</section>
			</Tabs>
			<ModalCreateForm isOpen={inspectionModals.forms.isOpen} onClose={inspectionModals.forms.closeModal} />
			<ModalCreateMeasures isOpen={inspectionModals.measures.isOpen} onClose={inspectionModals.measures.closeModal} />
			<ModalCreateFields isOpen={inspectionModals.fields.isOpen} onClose={inspectionModals.fields.closeModal} />
			<ModalCreateCellComponent isOpen={inspectionModals.components.isOpen} onClose={inspectionModals.components.closeModal} />
			<ModalCreateCellByProductType isOpen={inspectionModals.cellByProductType.isOpen} onClose={inspectionModals.cellByProductType.closeModal} />
			<ModalCreateCollabSector isOpen={inspectionModals.collaborators.isOpen} onClose={inspectionModals.collaborators.closeModal} />
			<ModalCreateFormLink isOpen={inspectionModals.formLinks.isOpen} onClose={inspectionModals.formLinks.closeModal} />
		</div>
	);
};
