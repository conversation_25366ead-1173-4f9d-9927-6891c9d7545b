import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "../../../../../core/toast";
import { createPostRequest } from "../../../../../shared/lib/requests";
import { IMessageGlobalReturn } from "../../../../../shared/types/requests/message.type";
import { CELL_COMPONENTS_ENDPOINTS } from "../../../api/endpoints";
import { inspectionKeys } from "../../../constants/query/keys";
import { ICreateCellByComponentType } from "../../../types/cell-components/dtos/create.dto";

export const useCreateCellByComponentTypeMutation = (onClose?: () => void) => {
	const queryClient = useQueryClient();

	const createFormMutation = useMutation({
		mutationKey: inspectionKeys.cellByComponents.custom("create"),
		mutationFn: async (formData: ICreateCellByComponentType) => {
			const res = await createPostRequest<IMessageGlobalReturn>(CELL_COMPONENTS_ENDPOINTS.CREATE, formData);
			if (!res.success) throw new Error(res.data.message);
			return res.data;
		},
		onSuccess: () => {
			inspectionKeys.cellByComponents.invalidateAllLists(queryClient);
			onClose?.();
		},
	});

	return {
		createCellByComponentType: (formData: ICreateCellByComponentType) =>
			toast.promise(createFormMutation.mutateAsync(formData), {
				loading: "Criando célula por componente...",
				success: ({ message }) => message,
				error: ({ message }) => message,
			}),
		// Alias esperado pelos testes legados
		createCellComponent: (formData: ICreateCellByComponentType) =>
			toast.promise(createFormMutation.mutateAsync(formData), {
				loading: "Criando célula por componente...",
				success: ({ message }) => message,
				error: ({ message }) => message,
			}),
	};
};

// Alias compatível com testes existentes
export const useCreateCellByComponentMutation = useCreateCellByComponentTypeMutation;
