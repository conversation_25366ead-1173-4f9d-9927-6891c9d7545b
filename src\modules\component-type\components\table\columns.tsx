import { ColumnDef } from "@tanstack/react-table";
import { IComponentTypesDto } from "../../types/find-all.dto";
import { ComponentTypeActions } from "./actions";

export const componentTypeColumns: ColumnDef<IComponentTypesDto>[] = [
	{
		accessorKey: "name",
		header: () => <div className="pl-[10px] text-start font-semibold">Nome</div>,
		cell: ({ row }) => (
			<div className="flex items-center justify-start pl-[10px]">
				<div className="max-w-[300px]">
					<span className="text-text-primary block truncate font-medium" title={row.original.name}>
						{row.original.name}
					</span>
				</div>
			</div>
		),
		size: 300,
	},
	{
		id: "actions",
		header: () => <div className="pr-[10px] text-right font-semibold">A<PERSON>ões</div>,
		cell: ({ row }) => <ComponentTypeActions componentTypeId={row.original.id} name={row.original.name} />,
		size: 80,
	},
];
