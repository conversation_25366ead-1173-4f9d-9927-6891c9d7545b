import { ACTIVITY_SUBJECTS } from "@/modules/activity/constants/subjects";
import { CELL_SUBJECTS } from "@/modules/cell/constants/subjects";
import { COLLABORATOR_SUBJECTS } from "@/modules/collaborator/constants/subjects";
import { INSPECTION_SUBJECTS } from "@/modules/inspection/constants/permissions/subjects";
import { PRODUCT_TYPE_SUBJECTS } from "@/modules/product-type/constants/subjects";
import { SECTOR_SUBJECTS } from "@/modules/sector/constants/subjects";
import { USER_SUBJECTS } from "@/modules/user/constants/subjects";
import { COMPONENT_TYPE_SUBJECTS } from "../../modules/component-type/constants/subjects";

export const SUBJECT_LIST = {
	ALL: "all",
	...INSPECTION_SUBJECTS,
	...SECTOR_SUBJECTS,
	...PRODUCT_TYPE_SUBJECTS,
	...COLLABORATOR_SUBJECTS,
	...ACTIVITY_SUBJECTS,
	...CELL_SUBJECTS,
	...USER_SUBJECTS,
	...COMPONENT_TYPE_SUBJECTS,
} as const;

export type TPermissionSubject = (typeof SUBJECT_LIST)[keyof typeof SUBJECT_LIST];

export const SUBJECTS = {
	...SUBJECT_LIST,
	LIST: Object.values(SUBJECT_LIST) as TPermissionSubject[],
};
