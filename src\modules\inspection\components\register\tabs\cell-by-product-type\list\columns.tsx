import { ColumnDef } from "@tanstack/react-table";
import { ICellByProductTypeDto } from "../../../../../types/cell-by-product-type/dtos/find-all.dto";
import { CellByProductTypeActions } from "./actions";

export const inspectionCellByProductTypeColumns: ColumnDef<ICellByProductTypeDto>[] = [
	{
		accessorKey: "cellName",
		header: () => <div className="pl-[10px] text-start font-semibold">Nome da Célula</div>,
		cell: ({ row }) => (
			<div className="flex items-center justify-start px-2 pl-[10px]">
				<span className="text-text-primary max-w-[200px] truncate text-center font-medium">{row.original.cellName}</span>
			</div>
		),
	},
	{
		accessorKey: "productTypeName",
		header: () => <div className="text-center font-semibold">Tipo de Produto</div>,
		cell: ({ row }) => (
			<div className="flex items-center justify-center px-2">
				<span className="text-text-primary max-w-[200px] truncate text-center font-medium">{row.original.productTypeName}</span>
			</div>
		),
	},
	{
		accessorKey: "actions",
		header: () => <div className="pr-[10px] text-end font-semibold">Ações</div>,
		cell: ({ row }) => (
			<div className="flex items-center justify-end px-2 pr-[10px]">
				<CellByProductTypeActions id={String(row.original.id)} name={row.original.cellName} />
			</div>
		),
	},
];
