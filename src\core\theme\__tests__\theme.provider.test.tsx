import { fireEvent, render, screen, waitFor } from "@testing-library/react";
import { Provider, createStore } from "jotai";
import React from "react";
import { useTheme } from "../hooks/use-theme.hook";
import { ThemeProvider } from "../providers/theme.provider";

const TestComponent = () => {
	const { theme, setDark, setLightGreen, toggle } = useTheme();
	return (
		<div>
			<span data-testid="theme-value">{theme}</span>
			<button onClick={setDark}>dark</button>
			<button onClick={setLightGreen}>light-green</button>
			<button onClick={toggle}>toggle</button>
		</div>
	);
};

const customRender = (ui: React.ReactElement) => {
	const store = createStore();
	return render(<Provider store={store}>{ui}</Provider>);
};

describe("ThemeProvider", () => {
	beforeAll(() => {
		// mock matchMedia
		Object.defineProperty(window, "matchMedia", {
			writable: true,
			value: (query: string) => ({
				matches: query.includes("dark") ? false : true,
				media: query,
				onchange: null,
				addEventListener: () => {},
				removeEventListener: () => {},
				addListener: () => {},
				removeListener: () => {},
				dispatchEvent: () => false,
			}),
		});
	});
	it("aplica tema inicial (light ou dark) e permite alternância", () => {
		customRender(
			<ThemeProvider>
				<TestComponent />
			</ThemeProvider>,
		);

		const themeValue = screen.getByTestId("theme-value");
		expect(["light", "dark", "light-green", "dark-green"]).toContain(themeValue.textContent);
	});

	it("altera para dark e light-green via ações", async () => {
		customRender(
			<ThemeProvider>
				<TestComponent />
			</ThemeProvider>,
		);
		const themeValue = screen.getByTestId("theme-value");
		const darkBtn = screen.getByText("dark");
		const lightGreenBtn = screen.getByText("light-green");

		fireEvent.click(darkBtn);
		await waitFor(() => expect(themeValue.textContent).toBe("dark"));
		const toggleBtn = screen.getByText("toggle");
		fireEvent.click(toggleBtn);
		await waitFor(() => expect(themeValue.textContent).toBe("light-green"));
		fireEvent.click(lightGreenBtn);
		await waitFor(() => expect(themeValue.textContent).toBe("light-green"));
	});
});
