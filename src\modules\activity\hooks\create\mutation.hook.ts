import { SUBJECTS } from "@/config/permissions";
import { usePermissions } from "@/shared/hooks/permissions/permissions.hook";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "../../../../core/toast";
import { createPostRequest } from "../../../../shared/lib/requests";
import { IMessageGlobalReturn } from "../../../../shared/types/requests/message.type";
import { ACTIVITY_ENDPOINTS } from "../../api/endpoints";
import { activityQueryKeys } from "../../constants/query";
import { TCreateActivity } from "../../validators/create";

export const useCreateActivityMutation = (onClose: () => void) => {
	const queryClient = useQueryClient();
	const { canCreate } = usePermissions();

	const createMutation = useMutation({
		mutationKey: activityQueryKeys.custom("create"),
		mutationFn: async (activity: TCreateActivity) => {
			if (!canCreate(SUBJECTS.ACTIVITY)) throw new Error("Você não tem permissão para criar atividades");
			const { data, success } = await createPostRequest<IMessageGlobalReturn>(ACTIVITY_ENDPOINTS.CREATE, activity);
			if (!success) throw new Error(data.message);
			return data;
		},
		onSuccess: () => {
			activityQueryKeys.invalidateAll(queryClient);
			onClose();
		},
	});

	return {
		createActivity: (form: TCreateActivity) =>
			toast.promise(createMutation.mutateAsync(form), {
				loading: "Criando atividade...",
				success: ({ message }) => message,
				error: ({ message }) => message,
			}),
		...createMutation,
	};
};
