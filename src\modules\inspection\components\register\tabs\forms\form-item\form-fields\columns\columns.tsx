import { ICreateFieldForm } from "@/modules/inspection/validators/form/create-field";
import { ColumnDef } from "@tanstack/react-table";
import { requiredLabel } from "../../form";
import { DragHandle } from "../items/drag-handle";
import { getColumnWidths } from "../utils/column-widths";
import { InspectionFormActionsRow } from "./actions-row";
import { InspectionFormBiFilterRow } from "./bi-filter-row";
import { InspectionFormFieldRow } from "./field-row";
import { InspectionFormFieldTypeRow } from "./field-type-row";
import { InspectionFormMeasureRow } from "./measure-row";
import { InspectionFormNicknameRow } from "./nickname-row";
import { InspectionFormRequiredRow } from "./required-row";

const columnWidths = getColumnWidths();

export const inspectionFormColumns: ColumnDef<ICreateFieldForm>[] = [
	{
		id: "drag-handle",
		header: () => null,
		cell: ({ row }) => <DragHandle id={row.original.tempId} />,
		meta: { width: columnWidths["drag-handle"] },
	},
	{
		id: "field-name",
		header: () => requiredLabel("Campo"),
		cell: ({ row, ...context }) => <InspectionFormFieldRow row={row} mode={(context as { mode?: "create" | "edit" | "view" }).mode} />,
		meta: { width: columnWidths["field-name"] },
	},
	{
		id: "nickname",
		header: "Abreviação",
		cell: ({ row, ...context }) => <InspectionFormNicknameRow row={row} mode={(context as { mode?: "create" | "edit" | "view" }).mode} />,
		meta: { width: columnWidths["nickname"] },
	},
	{
		id: "field-type",
		header: () => requiredLabel("Tipo"),
		cell: ({ row, ...context }) => <InspectionFormFieldTypeRow row={row} mode={(context as { mode?: "create" | "edit" | "view" }).mode} />,
		meta: { width: columnWidths["field-type"] },
	},
	{
		id: "measure",
		header: "Medida",
		cell: ({ row, ...context }) => <InspectionFormMeasureRow row={row} mode={(context as { mode?: "create" | "edit" | "view" }).mode} />,
		meta: { width: columnWidths["measure"] },
	},
	{
		id: "bi-filter",
		header: "Filtro BI",
		cell: ({ row, ...context }) => <InspectionFormBiFilterRow row={row} mode={(context as { mode?: "create" | "edit" | "view" }).mode} />,
		meta: { width: columnWidths["bi-filter"] },
	},
	{
		id: "required",
		header: "Obrigatório",
		cell: ({ row, ...context }) => <InspectionFormRequiredRow row={row} mode={(context as { mode?: "create" | "edit" | "view" }).mode} />,
		meta: { width: columnWidths["required"] },
	},
	{
		id: "actions",
		header: "Ações",
		cell: ({ row, ...context }) => <InspectionFormActionsRow row={row} mode={(context as { mode?: "create" | "edit" | "view" }).mode} />,
		meta: { width: columnWidths["actions"] },
	},
];
