import { Modal } from "@/shared/components/custom/modal";
import { But<PERSON> } from "@/shared/components/shadcn/button";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/shared/components/shadcn/form";
import { Input } from "@/shared/components/shadcn/input";
import { Save, X } from "lucide-react";
import { requiredLabel } from "../../../inspection/components/register/tabs/forms/form-item/form";
import { useCreateActivityForm } from "../../hooks/create/form.hook";
import { useCreateActivityMutation } from "../../hooks/create/mutation.hook";

interface CreateActivityModalProps {
	isOpen: boolean;
	onClose: () => void;
}

export const CreateActivityModal = ({ isOpen, onClose }: CreateActivityModalProps) => {
	const form = useCreateActivityForm();

	const handleClose = () => {
		form.reset();
		onClose();
	};

	const { createActivity, isPending } = useCreateActivityMutation(handleClose);

	return (
		<Modal isOpen={isOpen} onClose={handleClose} title="Adicionar Novo setor" description="Crie um novo setor" size="xl" className="max-h-[90vh]">
			<div className="space-y-6">
				<Form {...form}>
					<form onSubmit={form.handleSubmit(data => createActivity(data))} className="space-y-4">
						<FormField
							control={form.control}
							name="name"
							render={({ field }) => (
								<FormItem>
									<FormLabel>{requiredLabel("Nome")}</FormLabel>
									<FormControl>
										<Input
											autoFocus
											autoComplete="off"
											placeholder="Digite o nome do setor..."
											{...field}
											className="focus:border-primary/50"
										/>
									</FormControl>
									<FormMessage />
								</FormItem>
							)}
						/>
						<div className="flex justify-end gap-3">
							<Button type="button" variant="ghost" onClick={handleClose}>
								<X className="size-4" />
								Cancelar
							</Button>
							<Button type="submit" disabled={isPending} className="flex items-center gap-2">
								<Save className="size-4" />
								{isPending ? "Salvando..." : "Salvar"}
							</Button>
						</div>
					</form>
				</Form>
			</div>
		</Modal>
	);
};
