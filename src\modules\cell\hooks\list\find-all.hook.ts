"use client";
import { usePermissions } from "@/shared/hooks/permissions/permissions.hook";
import { processQueryResponsePaginated } from "@/shared/lib/query/process-query-response";
import { createGetRequest } from "@/shared/lib/requests";
import { IBaseHookPaginatedReturn } from "@/shared/types/hooks/hook-paginated.type";
import { IPaginationParameters } from "@/shared/types/pagination/types";
import { IResponsePaginated } from "@/shared/types/requests/response-paginated.type";
import { useQuery } from "@tanstack/react-query";
import { CELL_ENDPOINTS } from "../../api/endpoints";
import { cellQueryKeys } from "../../constants/query";
import { CELL_SUBJECTS } from "../../constants/subjects";
import { ICellDto } from "../../types/find-all.dto";

export const useFindAllCell = ({ page, limit, search }: IPaginationParameters): IBaseHookPaginatedReturn<ICellDto> => {
	const { canRead } = usePermissions();

	const { data, isLoading, isFetched } = useQuery({
		queryKey: cellQueryKeys.list({ page, limit, search }),
		queryFn: () => createGetRequest<IResponsePaginated<ICellDto>>(CELL_ENDPOINTS.FIND_ALL({ page, limit, search })),
		enabled: canRead(CELL_SUBJECTS.CELL),
	});

	return {
		...processQueryResponsePaginated<ICellDto>(data, isFetched),
		isLoading,
	};
};

export const useFindAllCellForSelect = (params: { limit: number; search: string; page: number }) => {
	const { page, limit, search } = params;
	const { data, isLoading, hasError, error } = useFindAllCell({ page, limit, search });

	return {
		data,
		isLoading,
		hasError,
		error,
	};
};
