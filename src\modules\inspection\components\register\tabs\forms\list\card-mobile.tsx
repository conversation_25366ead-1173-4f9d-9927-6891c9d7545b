import { INSPECTION_SUBJECTS } from "@/modules/inspection/constants/permissions/subjects";
import { ProtectedComponent } from "@/shared/components/auth/protected";
import { Badge } from "@/shared/components/shadcn/badge";
import { <PERSON>ton } from "@/shared/components/shadcn/button";
import { Card, CardContent } from "@/shared/components/shadcn/card";
import { Separator } from "@/shared/components/shadcn/separator";
import { useModal } from "@/shared/hooks/utils/modal.hook";
import { Co<PERSON>, Eye, Link, Trash } from "lucide-react";
import { IFormDto } from "../../../../../types/forms/dtos/find-all.dto";
import { ConfirmCloneFormModal } from "../clone/confirm-modal";
import { ConfirmDeleteFormModal } from "../delete/confirm-modal";
import { ModalEditForm } from "../edit/modal";

interface IFormCardMobileProps {
	form: IFormDto;
	index: number;
}

type ModalKey = "edit" | "clone" | "delete";

const actions: { icon: React.ElementType; title: string; color: string; modal: Modal<PERSON>ey; action: "update" | "create" | "delete" }[] = [
	{ icon: Eye, title: "Editar", color: "text-primary bg-primary/10", modal: "edit", action: "update" },
	{ icon: Copy, title: "Clonar", color: "text-primary bg-primary/10", modal: "clone", action: "create" },
	{ icon: Trash, title: "Excluir", color: "text-red-500 bg-red-500/10", modal: "delete", action: "delete" },
];

export const FormCardMobile = ({ form }: IFormCardMobileProps) => {
	const modals: Record<ModalKey, ReturnType<typeof useModal>> = {
		edit: useModal(),
		clone: useModal(),
		delete: useModal(),
	};

	return (
		<>
			<Card className="bg-card relative border shadow-sm hover:shadow-md">
				<CardContent>
					<div className="mb-3 flex items-start justify-between">
						<h3 className="mr-2 line-clamp-2 flex-1 text-base leading-tight font-semibold">
							{form.title}
							{!form.canUpdate && (
								<Badge variant="secondary" className="ml-2 text-xs">
									<Link />
								</Badge>
							)}
						</h3>
						<div className="bg-primary h-1.5 w-1.5 rounded-full opacity-60" />
					</div>
					<div className="mb-4 space-y-2">
						<div className="flex items-center justify-between">
							<span className="text-muted-foreground text-xs">Nomenclatura</span>
							<Badge variant="outline" className="bg-muted text-foreground px-2 py-0.5 font-mono text-xs">
								{form.nomenclature}
							</Badge>
						</div>
						<div className="flex items-center justify-between">
							<span className="text-muted-foreground text-xs">Revisão</span>
							<span className="bg-primary text-primary-foreground flex h-5 w-5 items-center justify-center rounded-full text-xs font-medium">
								{form.revision}
							</span>
						</div>
					</div>
					<Separator className="my-4" />
					<div className="flex gap-1.5">
						{actions.map(({ icon: Icon, title, color, modal, action }, idx) => (
							<ProtectedComponent key={idx} action={action} subject={INSPECTION_SUBJECTS.INSPECTION_FORM}>
								<Button
									key={idx}
									size="sm"
									variant="outline"
									onClick={modals[modal].openModal}
									className={`h-8 flex-1 border-none px-2 text-xs font-medium ${color}`}
								>
									<Icon className="h-3 w-3" />
									<span>{modal === "edit" ? (form.canUpdate ? "Editar" : "Visualizar") : title}</span>
								</Button>
							</ProtectedComponent>
						))}
					</div>
				</CardContent>
			</Card>
			<ModalEditForm isOpen={modals.edit.isOpen} onClose={modals.edit.closeModal} formId={form.id} canEdit={form.canUpdate} />
			<ConfirmCloneFormModal isOpen={modals.clone.isOpen} onClose={modals.clone.closeModal} title={form.title} formId={form.id} />
			<ConfirmDeleteFormModal isOpen={modals.delete.isOpen} onClose={modals.delete.closeModal} title={form.title} formId={form.id} />
		</>
	);
};
