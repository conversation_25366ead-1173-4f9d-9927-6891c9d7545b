import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "../../../../core/toast";
import { usePermissions } from "../../../../shared/hooks/permissions/permissions.hook";
import { createDeleteRequest } from "../../../../shared/lib/requests";
import { IMessageGlobalReturn } from "../../../../shared/types/requests/message.type";
import { COMPONENTS_TYPES_ENDPOINTS } from "../../api/endpoints";
import { componentsKeys } from "../../constants/query";
import { COMPONENT_TYPE_SUBJECTS } from "../../constants/subjects";

export const useDeleteComponentTypeMutation = () => {
	const queryClient = useQueryClient();
	const { canDelete } = usePermissions();

	const mutation = useMutation({
		mutationKey: componentsKeys.custom("delete"),
		mutationFn: async (id: number) => {
			if (!canDelete(COMPONENT_TYPE_SUBJECTS.COMPONENT_TYPE)) throw new Error("Você não tem permissão para deletar este tipo de componente.");
			const { data, success } = await createDeleteRequest<IMessageGlobalReturn>(COMPONENTS_TYPES_ENDPOINTS.DELETE(id));
			if (!success) throw new Error(data.message);
			return data;
		},
		onSuccess: () => componentsKeys.invalidateAll(queryClient),
	});

	return {
		deleteComponentType: (id: number) =>
			toast.promise(mutation.mutateAsync(id), {
				loading: "Deletando tipo de componente...",
				success: ({ message }) => message,
				error: ({ message }) => message,
			}),
	};
};
