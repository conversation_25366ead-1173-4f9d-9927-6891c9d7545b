import { usePermissions } from "@/shared/hooks/permissions/permissions.hook";
import { processQueryResponsePaginated } from "@/shared/lib/query/process-query-response";
import { IBaseHookPaginatedReturn } from "@/shared/types/hooks/hook-paginated.type";
import { IPaginationParameters } from "@/shared/types/pagination/types";
import { useQuery } from "@tanstack/react-query";
import { createGetRequest } from "../../../../shared/lib/requests";
import { IResponsePaginated } from "../../../../shared/types/requests/response-paginated.type";
import { SECTOR_ENDPOINTS } from "../../api/endpoints";
import { sectorQueryKeys } from "../../constants/query";
import { SECTOR_SUBJECTS } from "../../constants/subjects";
import { ISectorDto } from "../../types/find-all.dto";

export const useFindAllSector = ({ page, limit, search }: IPaginationParameters): IBaseHookPaginatedReturn<ISectorDto> => {
	const { canRead } = usePermissions();

	const { data, isLoading, isFetched } = useQuery({
		queryKey: sectorQueryKeys.list({ page, limit, search }),
		queryFn: () => createGetRequest<IResponsePaginated<ISectorDto>>(SECTOR_ENDPOINTS.FIND_ALL({ page, limit, search })),
		enabled: canRead(SECTOR_SUBJECTS.SECTOR),
	});

	return {
		...processQueryResponsePaginated(data, isFetched),
		isLoading,
	};
};
