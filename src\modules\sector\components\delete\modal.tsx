import { Modal } from "@/shared/components/custom/modal";
import { But<PERSON> } from "@/shared/components/shadcn/button";
import { Trash } from "lucide-react";
import { useDeleteSectorMutation } from "../../hooks/delete/mutation.hook";

interface DeleteSectorModalProps {
	isOpen: boolean;
	onClose: () => void;
	id: string;
	name: string;
}

export const DeleteSectorModal = ({ isOpen, onClose, id, name }: DeleteSectorModalProps) => {
	const { deleteSector } = useDeleteSectorMutation();

	const handleConfirm = () => {
		deleteSector(id);
		onClose();
	};

	if (!id) return null;

	return (
		<Modal showCloseButton={false} isOpen={isOpen} onClose={onClose}>
			<div className="flex flex-col items-center space-y-4 p-2 text-center">
				<Trash className="h-12 w-12 text-red-500" />

				<div className="space-y-2">
					<h2 className="text-xl font-semibold">Deletar setor</h2>
					<p className="text-sm text-gray-600">
						Tem certeza que deseja excluir este setor? <span className="text-primary font-medium">{name}</span>
					</p>
				</div>
				<div className="flex gap-3 pt-2">
					<Button variant="outline" onClick={onClose}>
						Cancelar
					</Button>
					<Button className="bg-red-400 hover:bg-red-500" onClick={handleConfirm}>
						Confirmar
					</Button>
				</div>
			</div>
		</Modal>
	);
};
