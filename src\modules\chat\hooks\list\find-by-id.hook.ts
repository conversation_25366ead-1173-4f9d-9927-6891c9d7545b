"use client";

import { usePermissions } from "@/shared/hooks/permissions/permissions.hook";
import { processQueryById } from "@/shared/lib/query/process-query-by-id";
import { createGetRequest } from "@/shared/lib/requests";
import { IBaseHookFindByIdReturn } from "@/shared/types/hooks/hook-find-by-id.type";
import { useQuery } from "@tanstack/react-query";
import { CHAT_ENDPOINTS } from "../../api/endpoints";
import { chatKeys } from "../../constants/query";
import { IKnowledgeDetailsDto } from "../../types/dtos/find-by-id-knowledge.dto";

export const useFindKnowledgeById = (id: string, enabled = true): IBaseHookFindByIdReturn<IKnowledgeDetailsDto> => {
	const { canRead } = usePermissions();

	const { data, isLoading, isFetched } = useQuery({
		queryKey: chatKeys.detail(id.toString()),
		queryFn: () => createGetRequest<IKnowledgeDetailsDto>(CHAT_ENDPOINTS.FIND_KNOWLEDGE_BY_ID(id.toString())),
		enabled: canRead("all") && Boolean(id) && enabled,
	});

	return {
		...processQueryById(data, isFetched),
		isLoading,
	};
};
