import { axiosInstance } from "@/config/api/instance";
import { pathService } from "@/config/path-manager/service";
import { ThemeVariant, isThemeVariant } from "@/core/theme/atoms/theme.atom";
import { THEME_PERSISTENCE_CONFIG } from "@/core/theme/constants/preferences";
import { AxiosError } from "axios";
import { cookies } from "next/headers";

interface ILoginRequestParams {
	redirectPath: string;
	theme: ThemeVariant;
}

interface IBackendResponse {
	status: number;
	location?: string;
	errorText?: string;
}

async function extractThemeFromCookies(): Promise<ThemeVariant> {
	try {
		const cookieStore = await cookies();
		const themeCookie = cookieStore.get(THEME_PERSISTENCE_CONFIG.cookieName);

		if (themeCookie?.value) {
			const parsedTheme = JSON.parse(themeCookie.value);
			if (isThemeVariant(parsedTheme)) return parsedTheme;
		}
	} catch (error) {
		console.warn("Erro ao extrair tema do cookie:", error);
	}

	return "light";
}

export async function extractLoginParams(request: URL): Promise<ILoginRequestParams> {
	const theme = await extractThemeFromCookies();

	return {
		redirectPath: request.searchParams.get("redirect") || "/",
		theme,
	};
}

export function createErrorResponse(error: string, message: string, status: number): Response {
	return Response.json({ error, message }, { status });
}

export async function fetchBackendLoginUrl(redirectPath: string, theme: ThemeVariant): Promise<IBackendResponse> {
	try {
		const params: Record<string, string> = {};

		if (redirectPath !== "/") {
			params.redirect = redirectPath;
		}

		// Sempre incluir o tema como parâmetro
		params.theme = theme;

		const response = await axiosInstance.get("/auth/login", {
			params,
			maxRedirects: 0,
			validateStatus: status => status < 400,
			withCredentials: true,
		});
		return {
			status: response.status,
			location: response.headers.location,
			errorText: response.status !== 302 ? response.data : undefined,
		};
	} catch (error) {
		const axiosError = error as AxiosError;
		if (axiosError.response?.status === 302) {
			return {
				status: 302,
				location: axiosError.response.headers?.location,
			};
		}
		if (!axiosError.response) {
			const isNetworkError =
				axiosError.code === "NETWORK_ERROR" ||
				axiosError.code === "ECONNREFUSED" ||
				axiosError.code === "ENOTFOUND" ||
				axiosError.message.includes("Network Error");

			const isTimeoutError = axiosError.code === "ECONNABORTED" || axiosError.code === "ETIMEDOUT" || axiosError.message.includes("timeout");

			return {
				status: 0,
				errorText: isTimeoutError
					? "Tempo limite de conexão esgotado. Verifique sua conexão e tente novamente."
					: isNetworkError
						? "Não foi possível conectar ao servidor. Verifique sua conexão com a internet."
						: axiosError.message,
			};
		}

		return {
			status: axiosError.response.status,
			location: axiosError.response.headers?.location,
			errorText:
				typeof axiosError.response.data === "string"
					? axiosError.response.data
					: axiosError.response.data
						? JSON.stringify(axiosError.response.data)
						: axiosError.message,
		};
	}
}

export function isRedirectResponse(response: IBackendResponse): boolean {
	return response.status === 302;
}

export function handleRedirectResponse(response: IBackendResponse): Response {
	if (!response.location) throw new Error("URL de redirecionamento não encontrada na resposta do backend");
	return Response.redirect(response.location, 302);
}

export function handleBackendError(response: IBackendResponse): Response {
	const errorMessage = response.errorText || "Erro na comunicação com o servidor de autenticação";
	const status = response.status >= 400 ? response.status : 500;
	return createErrorResponse("backend_error", errorMessage, status);
}

export function isValidRedirectPath(path: string): boolean {
	if (path.includes("://") || path.startsWith("//") || !path.startsWith("/") || ["<", ">", '"', "'", "&"].some(char => path.includes(char))) return false;
	const specialPaths = ["/forbidden"];
	if (specialPaths.includes(path)) return true;
	const item = pathService.getItemByPath(path);
	return item !== undefined;
}
