import { useTableFieldsDrag } from "@/modules/inspection/hooks/form/create/table-fields-drag.hook";
import { ICreateFieldForm } from "@/modules/inspection/validators/form/create-field";
import { Table, TableBody } from "@/shared/components/shadcn/table";
import { closestCenter, DndContext, DragEndEvent, DragOverlay, DragStartEvent, MeasuringStrategy } from "@dnd-kit/core";
import { restrictToParentElement, restrictToVerticalAxis } from "@dnd-kit/modifiers";
import { SortableContext, verticalListSortingStrategy } from "@dnd-kit/sortable";
import { ColumnDef, getCoreRowModel, useReactTable } from "@tanstack/react-table";
import { useState } from "react";
import { SortableTableRow } from "./sortable-table-row";

interface ItemsFieldGroupProps {
	items: ICreateFieldForm[];
	groupId: string;
	columns: ColumnDef<ICreateFieldForm>[];
	isOverlay: boolean;
	mode: "create" | "edit" | "view";
}

export const ItemsFieldGroup = ({ items, groupId, columns, isOverlay, mode }: ItemsFieldGroupProps) => {
	const { sensorsFields, handleDragEndFieldsGroup, sortableFieldId, dataIdsFields, currentItems } = useTableFieldsDrag(items, groupId);
	const [activeId, setActiveId] = useState<string | null>(null);

	const table = useReactTable({
		data: currentItems,
		columns,
		getCoreRowModel: getCoreRowModel(),
	});

	const isEmptyGroup = groupId.startsWith("empty");
	const activeItem = activeId ? currentItems.find(item => item.tempId === activeId) : null;
	const isDragging = !!activeId;

	const handleDragStart = (event: DragStartEvent) => {
		setActiveId(String(event.active.id));
	};

	const handleDragEnd = (event: DragEndEvent) => {
		handleDragEndFieldsGroup(event);
		setActiveId(null);
	};

	return (
		<div
			key={`items-${groupId}`}
			className={`w-full overflow-hidden lg:block ${isDragging ? "dragging" : ""} ${isEmptyGroup ? "bg-background/50 border-primary/20" : "bg-background-secondary/30"}`}
		>
			<DndContext
				collisionDetection={closestCenter}
				modifiers={[restrictToVerticalAxis, restrictToParentElement]}
				onDragStart={handleDragStart}
				onDragEnd={handleDragEnd}
				sensors={sensorsFields}
				id={sortableFieldId}
				autoScroll={false}
				measuring={{ droppable: { strategy: MeasuringStrategy.Always } }}
			>
				<div className="relative">
					<Table className="w-full table-fixed">
						<TableBody className={`overflow-hidden ${!isEmptyGroup ? "divide-border/50 divide-y" : ""}`}>
							<SortableContext items={dataIdsFields} strategy={verticalListSortingStrategy}>
								{table.getRowModel().rows.map(row => (
									<SortableTableRow key={row.id} row={row} isOverlay={isOverlay} mode={mode} />
								))}
							</SortableContext>
						</TableBody>
					</Table>
				</div>
				<DragOverlay>
					{activeItem && (
						<div className="border-border bg-card w-full rounded-md border opacity-95 shadow-lg">
							<Table className="w-full table-fixed">
								<TableBody>
									{table
										.getRowModel()
										.rows.filter(row => row.original.tempId === activeId)
										.map(row => (
											<SortableTableRow key={`overlay-${row.id}`} row={row} isOverlay={true} mode={mode} />
										))}
								</TableBody>
							</Table>
						</div>
					)}
				</DragOverlay>
			</DndContext>
		</div>
	);
};
