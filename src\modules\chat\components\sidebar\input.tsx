import { SendHorizonal, Square } from "lucide-react";
import React, { useState } from "react";
import { Button } from "../../../../shared/components/shadcn/button";
import { Textarea } from "../../../../shared/components/shadcn/textarea";
import { cn } from "../../../../shared/lib/shadcn/utils";
import { useChatInput } from "../../hooks/handlers/handle-input.hook";

interface IChatInputProps {
	onSend: (content: string) => Promise<void>;
	onStop?: () => void;
	disabled?: boolean;
	placeholder?: string;
	isStreaming?: boolean;
}

export const ChatInput = React.forwardRef<HTMLTextAreaElement, IChatInputProps>(({ onSend, onStop, disabled, placeholder, isStreaming }, ref) => {
	const [inputValue, setInputValue] = useState("");

	const { canSend, canStop, handleKeyDown, handleSend, handleStop } = useChatInput({
		value: inputValue,
		onChange: setInputValue,
		onSend,
		onStop,
		disabled,
		isStreaming,
	});

	return (
		<div
			className={cn(
				`border-border from-header-bg-from via-header-bg-via to-header-bg-to relative flex items-end gap-3 overflow-hidden border-t bg-gradient-to-r px-6 py-4 shadow-sm backdrop-blur-md transition-all duration-300 ease-in-out`,
			)}
		>
			<div className="from-primary/5 to-primary/5 pointer-events-none absolute inset-0 bg-gradient-to-r via-transparent" />
			<div className="via-primary/30 absolute top-0 right-0 left-0 h-px bg-gradient-to-r from-transparent to-transparent" />
			<div className="relative z-10 flex-1">
				<Textarea
					ref={ref}
					value={inputValue}
					onChange={e => setInputValue(e.target.value)}
					onKeyDown={handleKeyDown}
					placeholder={placeholder}
					disabled={disabled}
					className="focus:border-primary focus:ring-primary/20 border-border bg-background text-foreground placeholder:text-muted-foreground max-h-32 min-h-[48px] resize-none overflow-y-auto rounded-xl px-4 py-3 shadow-sm transition-all duration-200 hover:shadow-md focus:shadow-md focus:ring-2 disabled:cursor-not-allowed disabled:opacity-50"
					rows={1}
				/>
			</div>
			{canStop ? (
				<Button
					onClick={handleStop}
					size="sm"
					className="group bg-destructive hover:bg-destructive/90 relative z-10 h-12 w-12 shrink-0 self-end rounded-xl p-0 shadow-sm transition-all duration-200 hover:shadow-md"
					aria-label="Parar streaming"
				>
					<Square className="h-6 w-6 transition-transform duration-200 group-hover:scale-110" />
				</Button>
			) : (
				<Button
					onClick={handleSend}
					disabled={!canSend}
					size="sm"
					className="bg-primary hover:bg-primary/90 group disabled:bg-muted relative z-10 h-12 w-12 shrink-0 self-end rounded-xl p-0 shadow-sm transition-all duration-200 hover:shadow-md disabled:cursor-not-allowed"
					aria-label="Enviar mensagem"
				>
					<SendHorizonal className="h-6 w-6 transition-transform duration-200 group-hover:translate-x-0.5 group-hover:scale-110" />
				</Button>
			)}
		</div>
	);
});

ChatInput.displayName = "ChatInput";
