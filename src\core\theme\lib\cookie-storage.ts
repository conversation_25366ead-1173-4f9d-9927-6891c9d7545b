"use client";

export type ThemeVariant = "light" | "dark" | "light-green" | "dark-green";

export const THEME_VARIANTS: ThemeVariant[] = ["light", "dark", "light-green", "dark-green"];

export const isThemeVariant = (value: unknown): value is ThemeVariant => typeof value === "string" && (THEME_VARIANTS as string[]).includes(value);

interface ICookieStorageOptions {
	maxAge?: number;
	path?: string;
	secure?: boolean;
	sameSite?: "strict" | "lax" | "none";
}

export interface IJotaiCookieStorage {
	getItem: (key: string, initialValue: ThemeVariant) => ThemeVariant;
	setItem: (key: string, value: ThemeVariant) => void;
	removeItem: (key: string) => void;
}

function createJotaiCookieStorage(options: ICookieStorageOptions = {}): IJotaiCookieStorage {
	const defaultOptions: Required<ICookieStorageOptions> = {
		maxAge: 365 * 24 * 60 * 60,
		path: "/",
		secure: process.env.NODE_ENV === "production",
		sameSite: "lax",
		...options,
	};

	const getCookie = (name: string): string | null => {
		if (typeof document === "undefined") return null;

		const value = `; ${document.cookie}`;
		const parts = value.split(`; ${name}=`);

		if (parts.length === 2) {
			const cookieValue = parts.pop()?.split(";").shift();
			return cookieValue || null;
		}

		return null;
	};

	const setCookie = (name: string, value: string): void => {
		if (typeof document === "undefined") return;

		const cookieOptions = [`${name}=${value}`, `max-age=${defaultOptions.maxAge}`, `path=${defaultOptions.path}`, `samesite=${defaultOptions.sameSite}`];

		if (defaultOptions.secure) {
			cookieOptions.push("secure");
		}

		document.cookie = cookieOptions.join("; ");
	};

	const removeCookie = (name: string): void => {
		if (typeof document === "undefined") return;

		document.cookie = `${name}=; max-age=0; path=${defaultOptions.path}`;
	};

	return {
		getItem: (key: string, initialValue: ThemeVariant): ThemeVariant => {
			try {
				const cookieValue = getCookie(key);
				if (cookieValue === null) return initialValue;

				const parsed = JSON.parse(cookieValue);
				if (isThemeVariant(parsed)) {
					return parsed;
				}

				return initialValue;
			} catch (error) {
				console.warn(`Erro ao ler cookie ${key}:`, error);
				return initialValue;
			}
		},

		setItem: (key: string, value: ThemeVariant): void => {
			try {
				const serializedValue = JSON.stringify(value);
				setCookie(key, serializedValue);
			} catch (error) {
				console.error(`Erro ao salvar cookie ${key}:`, error);
			}
		},

		removeItem: (key: string): void => {
			removeCookie(key);
		},
	};
}

export const themeCookieStorage = createJotaiCookieStorage({
	maxAge: 365 * 24 * 60 * 60,
	path: "/",
	secure: process.env.NODE_ENV === "production",
	sameSite: "lax",
});
