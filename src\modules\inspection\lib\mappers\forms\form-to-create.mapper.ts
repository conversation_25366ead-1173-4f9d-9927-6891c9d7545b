import { ICreateFormDTO } from "../../../types/forms/dtos/create-form.dto";
import { IFieldGroup } from "../../../types/forms/fields-table/fields-group.type";
import { ICreateForm } from "../../../validators/form/create";

export class InspectionFormToCreateMapper {
	static map(data: ICreateForm, fields: IFieldGroup[]): ICreateFormDTO {
		return {
			title: data.title,
			text: data.text,
			nomenclature: data.nomenclature,
			developerId: data.developer.id,
			approverId: data.approver.id,
			fields: fields.flatMap(field =>
				field.items.map(item => ({
					fieldId: item.field.id!,
					nickname: item.nickname ?? "",
					required: !!item.required,
					group: field.group ?? undefined,
					sequence: item.sequence!,
					typeId: Number(item.typeId!),
					measureId: item.measure?.id ? Number(item.measure.id!) : undefined,
					groupTitle: field.groupTitle ?? undefined,
					biFilter: !!item.biFilter,
					options: (item.options ?? []).map((opt, i) => ({
						sequence: opt.sequence ?? i + 1,
						option: opt.option ?? "",
					})),
				})),
			),
		};
	}
}
