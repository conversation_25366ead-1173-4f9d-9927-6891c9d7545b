import { useAtomValue, useSet<PERSON><PERSON> } from "jotai";
import { <PERSON><PERSON>, Trash2, X } from "lucide-react";
import { Button } from "../../../../shared/components/shadcn/button";
import { toggleChatStateAtom } from "../../atoms/controls/trigger.atom";
import { isMessagesAvailableAtom } from "../../atoms/session/info.atom";
import { removeSessionAtom } from "../../atoms/session/remove.atom";

export const ChatHeader = () => {
	const hasMessages = useAtomValue(isMessagesAvailableAtom);
	const onClose = useSetAtom(toggleChatStateAtom);
	const removeSession = useSetAtom(removeSession<PERSON>tom);

	return (
		<header
			className={`border-border from-header-bg-from via-header-bg-via to-header-bg-to relative flex items-center justify-between overflow-hidden border-b bg-gradient-to-r px-6 py-4 shadow-sm backdrop-blur-md transition-all duration-300 ease-in-out`}
		>
			<div className="from-primary/5 to-primary/5 pointer-events-none absolute inset-0 bg-gradient-to-r via-transparent" />
			<div className="relative z-10 flex items-center gap-3">
				<div className="bg-primary/10 dark:bg-primary/20 border-primary/20 dark:border-primary/30 rounded-controls flex h-10 w-10 items-center justify-center border shadow-sm transition-all duration-200 hover:scale-105">
					<Bot className="text-primary h-5 w-5" />
				</div>
				<div className="flex flex-col">
					<h2 className="text-foreground text-lg leading-tight font-semibold">Doorinha</h2>
					<span className="text-muted-foreground text-xs font-medium">Assistente Virtual</span>
				</div>
			</div>
			<div className="relative z-10 flex items-center gap-1">
				{hasMessages && (
					<Button
						onClick={removeSession}
						variant="ghost"
						size="sm"
						className="group rounded-controls text-muted-foreground hover:bg-destructive/10 hover:text-destructive h-9 w-9 p-0 transition-all duration-200"
						title="Limpar conversa"
					>
						<Trash2 className="h-4 w-4 transition-transform duration-200 group-hover:scale-110" />
					</Button>
				)}
				<Button
					onClick={onClose}
					variant="ghost"
					size="sm"
					className="group rounded-controls text-muted-foreground hover:bg-accent/50 hover:text-accent-foreground h-9 w-9 p-0 transition-all duration-200"
					title="Fechar chat"
				>
					<X className="h-4 w-4 transition-transform duration-200 group-hover:scale-110" />
				</Button>
			</div>
			<div className="via-primary/30 absolute right-0 bottom-0 left-0 h-px bg-gradient-to-r from-transparent to-transparent" />
		</header>
	);
};
