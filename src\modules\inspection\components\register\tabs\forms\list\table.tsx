import { INSPECTION_SUBJECTS } from "@/modules/inspection/constants/permissions/subjects";
import { useModalForEditingForms } from "@/modules/inspection/hooks/form/edit/edit-modal.hook";
import { useFindAllForms } from "@/modules/inspection/hooks/form/list/find-all.hook";
import { ISearchTerm } from "@/modules/inspection/types/tabs/search-term.type";
import { GenericEmptyState } from "@/shared/components/custom/empty";
import { TableLoading } from "@/shared/components/custom/loading";
import { Pagination } from "@/shared/components/custom/pagination/pagination";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/shared/components/shadcn/table";
import { useIsMobile } from "@/shared/hooks/shadcn/use-mobile";
import { flexRender, getCoreRowModel, useReactTable } from "@tanstack/react-table";
import { FormInputIcon } from "lucide-react";
import React from "react";
import { usePagination } from "../../../../../../../shared/hooks/utils";
import { ModalEditForm } from "../edit/modal";
import { FormCardMobile } from "./card-mobile";
import { inspectionFormColumns } from "./columns";

export const FormTable: React.FC<ISearchTerm & { onNew: () => void }> = ({ searchTerm, onNew }) => {
	const { isOpen, selectedId, openEditModal, closeEditModal } = useModalForEditingForms();
	const { currentPage, setCurrentPage, pageSize, setItemsPerPage } = usePagination();
	const isMobile = useIsMobile();
	const [rowSelection, setRowSelection] = React.useState({});

	const { data, pagination, isLoading, hasError, error } = useFindAllForms({
		page: currentPage,
		limit: pageSize,
		search: searchTerm || "",
	});

	const table = useReactTable({
		data: data ?? [],
		columns: inspectionFormColumns,
		state: { rowSelection },
		onRowSelectionChange: setRowSelection,
		enableRowSelection: true,
		getCoreRowModel: getCoreRowModel(),
		manualPagination: true,
		pageCount: pagination?.totalPages ?? 0,
	});

	const selectedCount = table.getFilteredSelectedRowModel().rows.length;

	const paginationProps = {
		currentPage: pagination?.currentPage ?? currentPage,
		totalPages: pagination?.totalPages ?? 0,
		pageSize: pagination?.itemsPerPage ?? pageSize,
		totalItems: pagination?.totalItems ?? 0,
		selectedCount: selectedCount,
		onPageChange: setCurrentPage,
		onPageSizeChange: (size: number) => {
			setItemsPerPage(size);
			setCurrentPage(1);
		},
		showPageSizeSelector: true,
		showSelectedInfo: true,
	};

	const Empty = () => (
		<GenericEmptyState
			title="Nenhum formulário encontrado"
			description={searchTerm ? "Nenhum formulário corresponde ao termo pesquisado." : "Ainda não há formulários cadastrados."}
			buttonText="Cadastrar novo formulário"
			onAction={onNew}
			icon={FormInputIcon}
			subject={INSPECTION_SUBJECTS.INSPECTION_FORM}
		/>
	);

	const renderMobile = () => {
		if (hasError) return <div className="text-center text-red-500">Erro ao carregar os dados: {error || "Ocorreu um erro desconhecido."}</div>;
		if (isLoading) return <TableLoading columns={inspectionFormColumns.length} />;
		if (!data || !data.length) return <Empty />;

		return (
			<>
				<div className="flex flex-col gap-4">
					{data.map((form, idx) => (
						<FormCardMobile key={form.id} form={form} index={idx} />
					))}
				</div>
				{pagination && <Pagination {...paginationProps} />}
			</>
		);
	};

	const renderDesktop = () => {
		if (hasError) {
			return (
				<div className="space-y-4">
					<div className="bg-background rounded-controls overflow-x-auto border">
						<div className="flex h-24 items-center justify-center text-red-500">
							Erro ao carregar os dados: {error || "Ocorreu um erro desconhecido."}
						</div>
					</div>
				</div>
			);
		}

		if (isLoading) {
			return (
				<div className="space-y-4">
					<div className="bg-background rounded-controls overflow-x-auto border">
						<Table className="table-fixed">
							<TableHeader className="from-primary to-primary/95 supports-[backdrop-filter]:bg-primary/80 sticky top-0 z-10 bg-gradient-to-r backdrop-blur">
								{table.getHeaderGroups().map(headerGroup => (
									<TableRow key={headerGroup.id}>
										{headerGroup.headers.map(header => {
											const colDef = header.column?.columnDef as unknown as { size?: number } | undefined;
											const style = colDef?.size && header.colSpan === 1 ? { width: `${colDef.size}px` } : undefined;
											return (
												<TableHead
													key={header.id}
													colSpan={header.colSpan}
													style={style}
													className="font-semibold whitespace-nowrap text-white"
												>
													{header.isPlaceholder ? null : flexRender(header.column.columnDef.header, header.getContext())}
												</TableHead>
											);
										})}
									</TableRow>
								))}
							</TableHeader>
							<TableBody>
								<TableLoading columns={inspectionFormColumns.length} rows={pageSize} />
							</TableBody>
						</Table>
					</div>
				</div>
			);
		}

	if (!table.getRowModel().rows.length && !isLoading) {
		return (
			<div className="flex-1">
				<Empty />
			</div>
		);
	}

		return (
			<div className="h-full flex-1 space-y-4">
				<div className="bg-background rounded-controls overflow-x-auto border">
					<Table className="table-fixed">
						<TableHeader className="bg-primary sticky top-0 z-10">
							{table.getHeaderGroups().map(headerGroup => (
								<TableRow key={headerGroup.id}>
									{headerGroup.headers.map(header => {
										const colDef = header.column?.columnDef as unknown as { size?: number } | undefined;
										const style = colDef?.size && header.colSpan === 1 ? { width: `${colDef.size}px` } : undefined;
										return (
											<TableHead
												key={header.id}
												colSpan={header.colSpan}
												style={style}
												className="font-semibold whitespace-nowrap text-white"
											>
												{header.isPlaceholder ? null : flexRender(header.column.columnDef.header, header.getContext())}
											</TableHead>
										);
									})}
								</TableRow>
							))}
						</TableHeader>
						<TableBody>
							{table.getRowModel().rows.map((row, idx) => (
								<TableRow
									key={row.id}
									onClick={() => {
										openEditModal(row.original.id);
									}}
									data-state={row.getIsSelected() && "selected"}
									className={`${idx % 2 === 0 ? "bg-muted/20" : "bg-background"} hover:bg-primary/10 cursor-pointer transition-colors`}
								>
									{row.getVisibleCells().map(cell => (
										<TableCell
											key={cell.id}
											className="h-[28px] max-w-[260px] overflow-hidden text-ellipsis whitespace-nowrap"
											title={String(cell.getValue() ?? "")}
										>
											{flexRender(cell.column.columnDef.cell, cell.getContext())}
										</TableCell>
									))}
								</TableRow>
							))}
						</TableBody>
					</Table>
				</div>
				{isOpen && selectedId && <ModalEditForm isOpen={isOpen} onClose={closeEditModal} formId={selectedId} canEdit={true} />}
				{pagination && (
					<div className="mt-4">
						<Pagination
							currentPage={pagination.currentPage}
							totalPages={pagination.totalPages}
							pageSize={pagination.itemsPerPage}
							totalItems={pagination.totalItems}
							selectedCount={selectedCount}
							onPageChange={setCurrentPage}
							onPageSizeChange={size => setItemsPerPage(size)}
							showPageSizeSelector
							showSelectedInfo
						/>
					</div>
				)}
			</div>
		);
	};

	return <div className="flex flex-1 flex-col space-y-4">{isMobile ? renderMobile() : renderDesktop()}</div>;
};
