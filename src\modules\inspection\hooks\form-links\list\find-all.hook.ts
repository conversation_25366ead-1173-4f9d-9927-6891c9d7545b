"use client";
import { INSPECTION_FORMS_LINKS_ENDPOINTS } from "@/modules/inspection/api/endpoints";
import { INSPECTION_SUBJECTS } from "@/modules/inspection/constants/permissions/subjects";
import { usePermissions } from "@/shared/hooks/permissions/permissions.hook";
import { processQueryResponsePaginated } from "@/shared/lib/query/process-query-response";
import { createGetRequest } from "@/shared/lib/requests";
import { IBaseHookPaginatedReturn } from "@/shared/types/hooks/hook-paginated.type";
import { IPaginationParameters } from "@/shared/types/pagination/types";
import { IResponsePaginated } from "@/shared/types/requests/response-paginated.type";
import { useQuery } from "@tanstack/react-query";
import { inspectionKeys } from "../../../constants/query/keys";
import { IFormLinkDto } from "../../../types/form-link/dtos/find-all.dto";

export const useFindAllFormLinks = (params: IPaginationParameters): IBaseHookPaginatedReturn<IFormLinkDto> => {
	const { canRead } = usePermissions();

	const { data, isLoading, isFetched } = useQuery({
		queryKey: inspectionKeys.formsLink.list({ ...params }),
		queryFn: () => createGetRequest<IResponsePaginated<IFormLinkDto>>(INSPECTION_FORMS_LINKS_ENDPOINTS.FIND_ALL(params)),
		enabled: canRead(INSPECTION_SUBJECTS.INSPECTION_FORM),
	});

	return {
		...processQueryResponsePaginated(data, isFetched),
		isLoading,
	};
};
