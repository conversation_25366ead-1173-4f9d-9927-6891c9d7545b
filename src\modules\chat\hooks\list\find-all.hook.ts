"use client";
import { usePermissions } from "@/shared/hooks/permissions/permissions.hook";
import { processQueryResponsePaginated } from "@/shared/lib/query/process-query-response";
import { createGetRequest } from "@/shared/lib/requests";
import { IBaseHookPaginatedReturn } from "@/shared/types/hooks/hook-paginated.type";
import { IPaginationParameters } from "@/shared/types/pagination/types";
import { IResponsePaginated } from "@/shared/types/requests/response-paginated.type";
import { useQuery } from "@tanstack/react-query";
import { CHAT_ENDPOINTS } from "../../api/endpoints";
import { chatKeys } from "../../constants/query";
import { IChatKnowledgeDto } from "../../types/dtos/find-all-knowledge.dto";

export const useFindAllChatKnowledge = (params: IPaginationParameters): IBaseHookPaginatedReturn<IChatKnowledgeDto> => {
	const { canRead } = usePermissions();

	const { data, isLoading, isFetched } = useQuery({
		queryKey: chatKeys.list({ ...params }),
		queryFn: () => createGetRequest<IResponsePaginated<IChatKnowledgeDto>>(CHAT_ENDPOINTS.FIND_ALL_KNOWLEDGE(params)),
		enabled: canRead("all"),
	});

	return {
		...processQueryResponsePaginated<IChatKnowledgeDto>(data, isFetched),
		isLoading,
	};
};
