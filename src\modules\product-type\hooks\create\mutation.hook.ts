"use client";

import { toast } from "@/core/toast";
import { usePermissions } from "@/shared/hooks/permissions/permissions.hook";
import { createPostRequest } from "@/shared/lib/requests";
import { IMessageGlobalReturn } from "@/shared/types/requests/message.type";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { PRODUCT_TYPE_ENDPOINTS } from "../../api/endpoints";
import { productTypeKeys } from "../../constants/query";
import { PRODUCT_TYPE_SUBJECTS } from "../../constants/subjects";
import { TCreateProductType } from "../../validators/create";

export const useCreateProductTypeMutation = (onClose?: () => void) => {
	const queryClient = useQueryClient();
	const { canCreate } = usePermissions();

	const createProductType = useMutation({
		mutationKey: productTypeKeys.custom("create"),
		mutationFn: async (productType: TCreateProductType) => {
			if (!canCreate(PRODUCT_TYPE_SUBJECTS.PRODUCT_TYPE)) throw new Error("Sem permissão para criar tipo de produto");
			const { data, success } = await createPostRequest<IMessageGlobalReturn>(PRODUCT_TYPE_ENDPOINTS.CREATE, productType);
			if (!success) throw new Error(data.message);
			return data;
		},
		onSuccess: () => {
			productTypeKeys.invalidateAll(queryClient);
			onClose?.();
		},
	});

	return {
		createProductType: (form: TCreateProductType) =>
			toast.promise(createProductType.mutateAsync(form), {
				loading: "Criando tipo de produto...",
				success: ({ message }) => message,
				error: ({ message }) => message,
			}),
		...createProductType,
	};
};
