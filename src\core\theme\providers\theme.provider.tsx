"use client";

import { use<PERSON><PERSON>, useAtomValue, useSetAtom } from "jotai";
import { PropsWithChildren, useEffect, useLayoutEffect, useRef } from "react";
import { applyTheme, getCurrentTheme, isThemeVariant, mountedAtom, themeAtom, userExplicitThemeAtom, type ThemeVariant } from "../atoms/theme.atom";

const ThemeManager = () => {
	const [mounted] = useAtom(mountedAtom);
	const explicit = useAtomValue(userExplicitThemeAtom);
	const current = useAtomValue(getCurrentTheme);
	const setTheme = useSetAtom(applyTheme);

	useEffect(() => {
		if (explicit) return;
		if (typeof window === "undefined" || typeof window.matchMedia !== "function") return;
		const mediaQuery = window.matchMedia("(prefers-color-scheme: dark)");

		const handleChange = () => {
			if (!mounted && !explicit) {
				setTheme(mediaQuery.matches ? "dark" : "light");
			}
		};

		mediaQuery.addEventListener("change", handleChange);
		return () => mediaQuery.removeEventListener("change", handleChange);
	}, [mounted, explicit, setTheme]);

	useEffect(() => {
		if (typeof document === "undefined") return;
		if (!document.documentElement.classList.contains(current)) {
			setTheme(current);
		}
	}, [current, setTheme]);

	return null;
};

export const ThemeProvider = ({ children }: PropsWithChildren) => {
	const setTheme = useSetAtom(applyTheme);
	const appliedRef = useRef<ThemeVariant | null>(null);
	const [storedTheme] = useAtom(themeAtom);
	useLayoutEffect(() => {
		const getCurrentAppliedTheme = (): ThemeVariant => {
			const classList = document.documentElement.classList;
			for (const variant of ["light", "dark", "light-green", "dark-green"] as ThemeVariant[]) {
				if (classList.contains(variant)) {
					return variant;
				}
			}
			return "light";
		};

		const appliedTheme = getCurrentAppliedTheme();
		if (isThemeVariant(storedTheme) && storedTheme !== appliedTheme) {
			setTheme(storedTheme);
		} else if (appliedRef.current !== appliedTheme) {
			appliedRef.current = appliedTheme;
		}
	}, [storedTheme, setTheme]);

	return (
		<>
			<ThemeManager />
			{children}
		</>
	);
};

export { ThemeManager };
