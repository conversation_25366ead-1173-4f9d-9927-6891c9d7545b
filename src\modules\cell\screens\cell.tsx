"use client";

import { useModal } from "@/shared/hooks/utils/modal.hook";
import { CreateCellModal } from "../components/create/modal";
import { TableCell } from "../components/list/table";

export const CellScreen = () => {
	const cellModal = useModal();

	return (
		<main id="cell" className="flex h-full w-full flex-1 flex-col gap-6">
			<TableCell onOpenModal={cellModal.toggleModal} />
			<CreateCellModal isOpen={cellModal.isOpen} onClose={cellModal.toggleModal} />
		</main>
	);
};
