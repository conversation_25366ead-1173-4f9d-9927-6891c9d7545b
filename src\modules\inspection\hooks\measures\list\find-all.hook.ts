"use client";
import { MEASURES_ENDPOINTS } from "@/modules/inspection/api/endpoints";
import { INSPECTION_SUBJECTS } from "@/modules/inspection/constants/permissions/subjects";
import { usePermissions } from "@/shared/hooks/permissions/permissions.hook";
import { processQueryResponsePaginated } from "@/shared/lib/query/process-query-response";
import { createGetRequest } from "@/shared/lib/requests";
import { IBaseHookPaginatedReturn } from "@/shared/types/hooks/hook-paginated.type";
import { IPaginationParameters } from "@/shared/types/pagination/types";
import { IResponsePaginated } from "@/shared/types/requests/response-paginated.type";
import { useQuery } from "@tanstack/react-query";
import { inspectionKeys } from "../../../constants/query/keys";

export interface IMeasuresDto {
	id: string;
	name: string;
	abbreviation: string;
}

export default function useFindAllMeasures({ page = 1, limit = 10, search = "" }: IPaginationParameters): IBaseHookPaginatedReturn<IMeasuresDto> {
	const { canRead } = usePermissions();

	const { data, isLoading, isFetched } = useQuery({
		queryKey: inspectionKeys.measures.list({ page, limit, search }),
		queryFn: () => createGetRequest<IResponsePaginated<IMeasuresDto>>(MEASURES_ENDPOINTS.FIND_ALL({ page, limit, search })),
		enabled: canRead(INSPECTION_SUBJECTS.INSPECTION_MEASURE),
	});

	return {
		...processQueryResponsePaginated(data, isFetched),
		isLoading,
	};
}
