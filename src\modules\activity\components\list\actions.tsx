import { ProtectedComponent } from "@/shared/components/auth/protected";
import { Button } from "@/shared/components/shadcn/button";
import { useModal } from "@/shared/hooks/utils/modal.hook";
import { Trash } from "lucide-react";
import { DeleteActivityModal } from "../delete/modal";

export const ActivityActions = ({ activityId, name }: { activityId: number; name: string }) => {
	const deleteModal = useModal();

	return (
		<div className="pr-[10px] text-right">
			<ProtectedComponent action="delete" subject="all">
				<Button
					data-row-ignore
					size="icon"
					onClick={e => {
						e.stopPropagation();
						deleteModal.openModal();
					}}
					className="group h-8 w-8 bg-red-500/5 hover:bg-red-500/30"
				>
					<Trash className="h-4 w-4 text-red-500 group-hover:text-white" />
				</Button>
			</ProtectedComponent>
			{deleteModal.isOpen && <DeleteActivityModal isOpen={deleteModal.isOpen} onClose={deleteModal.closeModal} name={name} id={activityId.toString()} />}
		</div>
	);
};
