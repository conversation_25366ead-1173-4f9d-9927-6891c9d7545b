import { INSPECTION_SUBJECTS } from "@/modules/inspection/constants/permissions/subjects";
import { useFindAllCollabBySector } from "@/modules/inspection/hooks/collaborator-by-sector/list/find-all.hook";
import { GenericEmptyState } from "@/shared/components/custom/empty";
import { TableLoading } from "@/shared/components/custom/loading";
import { flexRender, getCoreRowModel, useReactTable } from "@tanstack/react-table";
import { ComponentIcon } from "lucide-react";
import { useState } from "react";
import { Pagination } from "../../../../../../../shared/components/custom/pagination/pagination";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "../../../../../../../shared/components/shadcn/table";
import { usePagination } from "../../../../../../../shared/hooks/utils";
import { useIsMobile } from "../../../../../../../shared/hooks/utils/media-query.hook";
import { ISearchTerm } from "../../../../../types/tabs/search-term.type";
import { CollabBySectorCardMobile } from "./card-mobile";
import { inspectionCollabBySectorColumns } from "./columns";

export const CollabBySectorTable: React.FC<ISearchTerm & { onNew: () => void }> = ({ searchTerm, onNew }) => {
	const [rowSelection, setRowSelection] = useState({});
	const { currentPage, setCurrentPage, pageSize, setItemsPerPage } = usePagination();
	const isMobile = useIsMobile();

	const { data, isLoading, hasError, error, pagination } = useFindAllCollabBySector({
		page: currentPage,
		limit: pageSize,
		search: searchTerm,
	});

	const table = useReactTable({
		data,
		columns: inspectionCollabBySectorColumns,
		state: { rowSelection },
		onRowSelectionChange: setRowSelection,
		enableRowSelection: true,
		getCoreRowModel: getCoreRowModel(),
		manualPagination: true,
		pageCount: pagination?.totalPages ?? 0,
	});

	const selectedCount = table.getFilteredSelectedRowModel().rows.length;

	if (isMobile) {
		if (hasError) {
			return (
				<div className="space-y-4">
					<div className="h-24 text-center text-red-500">Erro ao carregar os dados: {error || "Ocorreu um erro desconhecido."}</div>
				</div>
			);
		}

		if (isLoading) {
			return (
				<div className="space-y-4">
					<div className="flex h-24 items-center justify-center">
						<TableLoading columns={1} rows={pageSize} />
					</div>
				</div>
			);
		}

		if (!data || !data.length) {
			return (
				<GenericEmptyState
					title="Nenhum colaborador encontrado"
					description={searchTerm ? "Nenhum colaborador corresponde ao termo pesquisado." : "Ainda não há colaboradores cadastrados."}
					buttonText="Cadastrar novo colaborador"
					onAction={onNew}
					icon={ComponentIcon}
					subject={INSPECTION_SUBJECTS.INSPECTION_COLLABORATOR_BY_SECTOR}
				/>
			);
		}

		return (
			<div className="space-y-4">
				{data.map(collab => (
					<CollabBySectorCardMobile key={collab.id} collab={collab} />
				))}
				{pagination && (
					<div className="mt-4">
						<Pagination
							currentPage={pagination.currentPage}
							totalPages={pagination.totalPages}
							pageSize={pagination.itemsPerPage}
							totalItems={pagination.totalItems}
							selectedCount={selectedCount}
							onPageChange={setCurrentPage}
							onPageSizeChange={size => {
								setItemsPerPage(size);
								setCurrentPage(1);
							}}
							showPageSizeSelector
							showSelectedInfo
						/>
					</div>
				)}
			</div>
		);
	}

	if (hasError) {
		return (
			<div className="space-y-4">
				<div className="bg-background rounded-controls overflow-x-auto border">
					<div className="flex h-24 items-center justify-center text-red-500">
						Erro ao carregar os dados: {error || "Ocorreu um erro desconhecido."}
					</div>
				</div>
			</div>
		);
	}

	if (isLoading) {
		return (
			<div className="space-y-4">
				<div className="bg-background rounded-controls overflow-x-auto border">
					<Table className="table-fixed">
						<TableHeader className="from-primary to-primary/95 supports-[backdrop-filter]:bg-primary/80 sticky top-0 z-10 bg-gradient-to-r backdrop-blur">
							{table.getHeaderGroups().map(headerGroup => (
								<TableRow key={headerGroup.id}>
									{headerGroup.headers.map(header => {
										const colDef = header.column?.columnDef as unknown as { size?: number } | undefined;
										const style = colDef?.size && header.colSpan === 1 ? { width: `${colDef.size}px` } : undefined;
										return (
											<TableHead
												key={header.id}
												colSpan={header.colSpan}
												style={style}
												className="font-semibold whitespace-nowrap text-white"
											>
												{header.isPlaceholder ? null : flexRender(header.column.columnDef.header, header.getContext())}
											</TableHead>
										);
									})}
								</TableRow>
							))}
						</TableHeader>
						<TableBody>
							<TableLoading columns={inspectionCollabBySectorColumns.length} rows={pageSize} />
						</TableBody>
					</Table>
				</div>
			</div>
		);
	}

	if (!table.getRowModel().rows.length && !isLoading) {
		return (
			<GenericEmptyState
				title="Nenhum colaborador encontrado"
				description={searchTerm ? "Nenhum colaborador corresponde ao termo pesquisado." : "Ainda não há colaboradores cadastrados."}
				buttonText="Cadastrar novo colaborador"
				onAction={onNew}
				icon={ComponentIcon}
				subject={INSPECTION_SUBJECTS.INSPECTION_COLLABORATOR_BY_SECTOR}
			/>
		);
	}

	return (
		<div className="flex-1 space-y-4">
			<div className="bg-background rounded-controls overflow-x-auto border">
				<Table className="table-fixed">
					<TableHeader className="bg-primary sticky top-0 z-10">
						{table.getHeaderGroups().map(headerGroup => (
							<TableRow key={headerGroup.id}>
								{headerGroup.headers.map(header => {
									const colDef = header.column?.columnDef as unknown as { size?: number } | undefined;
									const style = colDef?.size && header.colSpan === 1 ? { width: `${colDef.size}px` } : undefined;
									return (
										<TableHead
											key={header.id}
											colSpan={header.colSpan}
											style={style}
											className="font-semibold whitespace-nowrap text-white"
										>
											{header.isPlaceholder ? null : flexRender(header.column.columnDef.header, header.getContext())}
										</TableHead>
									);
								})}
							</TableRow>
						))}
					</TableHeader>
					<TableBody>
						{table.getRowModel().rows.map((row, idx) => (
							<TableRow
								key={row.id}
								data-state={row.getIsSelected() && "selected"}
								className={`${idx % 2 === 0 ? "bg-muted/20" : "bg-background"} hover:bg-primary/10 transition-colors`}
							>
								{row.getVisibleCells().map(cell => (
									<TableCell
										key={cell.id}
										className="h-[28px] max-w-[260px] overflow-hidden text-ellipsis whitespace-nowrap"
										title={String(cell.getValue() ?? "")}
									>
										{flexRender(cell.column.columnDef.cell, cell.getContext())}
									</TableCell>
								))}
							</TableRow>
						))}
					</TableBody>
				</Table>
			</div>
			{pagination && (
				<div className="mt-4">
					<Pagination
						currentPage={pagination.currentPage}
						totalPages={pagination.totalPages}
						pageSize={pagination.itemsPerPage}
						totalItems={pagination.totalItems}
						selectedCount={selectedCount}
						onPageChange={setCurrentPage}
						onPageSizeChange={size => setItemsPerPage(size)}
						showPageSizeSelector
						showSelectedInfo
					/>
				</div>
			)}
		</div>
	);
};
