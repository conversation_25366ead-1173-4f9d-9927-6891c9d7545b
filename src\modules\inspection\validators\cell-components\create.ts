import z from "zod";
import { Entity } from "../forms-links/create";

const requiredEntitySchema = (message: string) =>
	z.custom<Entity>(val => val && typeof val === "object" && typeof val.id === "number" && typeof val.name === "string", { message });

export const createCellComponentSchema = z.object({
	cell: requiredEntitySchema("A célula é obrigatória."),
	component: requiredEntitySchema("O componente é obrigatório."),
});

export type TCreateCellComponentTypeForm = z.infer<typeof createCellComponentSchema>;
