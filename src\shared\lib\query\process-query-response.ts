import { IBaseHookPaginatedReturn } from "@/shared/types/hooks/hook-paginated.type";
import { ApiResponse } from "@/shared/types/requests/request.type";
import { IResponsePaginated } from "@/shared/types/requests/response-paginated.type";

export const processQueryResponsePaginated = <T>(
	data: ApiResponse<IResponsePaginated<T>> | undefined,
	isFetched: boolean,
): Omit<IBaseHookPaginatedReturn<T>, "isLoading"> => {
	const isNoDataFound: boolean = Boolean((!data?.success && data?.status === 404) || (data?.success && data?.data.data.length === 0));
	const hasPermissionError: boolean = Boolean(!data?.success && data?.status === 403);

	return {
		data: data?.success ? data.data.data : [],
		pagination: data?.success
			? {
					totalItems: data.data.totalItems,
					itemsPerPage: data.data.itemsPerPage,
					currentPage: data.data.currentPage,
					totalPages: data.data.totalPages,
				}
			: null,
		hasError: isFetched && !data?.success && !isNoDataFound && !hasPermissionError,
		error: !data?.success && !isNoDataFound && !hasPermissionError ? data?.data?.message : undefined,
		isEmpty: isNoDataFound,
	};
};
