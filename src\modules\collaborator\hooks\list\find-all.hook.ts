"use client";
import { usePermissions } from "@/shared/hooks/permissions/permissions.hook";
import { processQueryResponsePaginated } from "@/shared/lib/query/process-query-response";
import { createGetRequest } from "@/shared/lib/requests";
import { IBaseHookPaginatedReturn } from "@/shared/types/hooks/hook-paginated.type";
import { IPaginationParameters } from "@/shared/types/pagination/types";
import { IResponsePaginated } from "@/shared/types/requests/response-paginated.type";
import { useQuery } from "@tanstack/react-query";
import { COLLABORATOR_ENDPOINTS } from "../../api/endpoints";
import { collaboratorQueryKeys } from "../../constants/query";
import { COLLABORATOR_SUBJECTS } from "../../constants/subjects";
import { ICollaboratorDto } from "../../types/find-all.dto";

export const useFindAllCollaborator = ({ page, limit, search }: IPaginationParameters): IBaseHookPaginatedReturn<ICollaboratorDto> => {
	const { canRead } = usePermissions();
	const { data, isLoading, isFetched } = useQuery({
		queryKey: collaboratorQueryKeys.list({ page, limit, search }),
		queryFn: () => createGetRequest<IResponsePaginated<ICollaboratorDto>>(COLLABORATOR_ENDPOINTS.FIND_ALL({ page, limit, search })),
		enabled: canRead(COLLABORATOR_SUBJECTS.COLLABORATOR),
	});

	return {
		...processQueryResponsePaginated(data, isFetched),
		isLoading,
	};
};
