"use client";

import { INSPECTION_SUBJECTS } from "@/modules/inspection/constants/permissions/subjects";
import { useFindAllFields } from "@/modules/inspection/hooks/fields/list/find-all.hook";
import { GenericEmptyState } from "@/shared/components/custom/empty";
import { TableLoading } from "@/shared/components/custom/loading";
import { Pagination } from "@/shared/components/custom/pagination/pagination";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/shared/components/shadcn/table";
import { flexRender, getCoreRowModel, useReactTable } from "@tanstack/react-table";
import { ComponentIcon } from "lucide-react";
import React from "react";
import { usePagination } from "../../../../../../../shared/hooks/utils";
import { ISearchTerm } from "../../../../../types/tabs/search-term.type";
import { columnsFields } from "./columns";

export function FieldsTable({ searchTerm, onNew }: ISearchTerm & { onNew: () => void }) {
	const [rowSelection, setRowSelection] = React.useState({});
	const { currentPage, setCurrentPage, pageSize, setItemsPerPage } = usePagination();

	const { data, pagination, isLoading, hasError, error } = useFindAllFields({
		page: currentPage,
		limit: pageSize,
		search: searchTerm || "",
	});

	const table = useReactTable({
		data: data,
		columns: columnsFields,
		state: { rowSelection },
		onRowSelectionChange: setRowSelection,
		enableRowSelection: true,
		getCoreRowModel: getCoreRowModel(),
		manualPagination: true,
		pageCount: pagination?.totalPages ?? 0,
	});

	const selectedCount = table.getFilteredSelectedRowModel().rows.length;

	if (hasError) {
		return (
			<div className="space-y-4">
				<div className="bg-background rounded-controls overflow-x-auto border">
					<div className="flex h-24 items-center justify-center text-red-500">
						Erro ao carregar os dados: {error || "Ocorreu um erro desconhecido."}
					</div>
				</div>
			</div>
		);
	}

	if (isLoading) {
		return (
			<div className="space-y-4">
				<div className="bg-background rounded-controls overflow-x-auto border">
					<Table className="table-fixed">
						<TableHeader className="from-primary to-primary/95 supports-[backdrop-filter]:bg-primary/80 sticky top-0 z-10 bg-gradient-to-r backdrop-blur">
							{table.getHeaderGroups().map(headerGroup => (
								<TableRow key={headerGroup.id}>
									{headerGroup.headers.map(header => {
										const colDef = header.column?.columnDef as unknown as { size?: number } | undefined;
										const style = colDef?.size && header.colSpan === 1 ? { width: `${colDef.size}px` } : undefined;
										return (
											<TableHead
												key={header.id}
												colSpan={header.colSpan}
												style={style}
												className="font-semibold whitespace-nowrap text-white"
											>
												{header.isPlaceholder ? null : flexRender(header.column.columnDef.header, header.getContext())}
											</TableHead>
										);
									})}
								</TableRow>
							))}
						</TableHeader>
						<TableBody>
							<TableLoading columns={columnsFields.length} rows={pageSize} />
						</TableBody>
					</Table>
				</div>
			</div>
		);
	}

	if (!table.getRowModel().rows.length && !isLoading) {
		return (
			<GenericEmptyState
				title="Nenhum campo encontrado"
				description={searchTerm ? "Nenhum campo corresponde ao termo pesquisado." : "Ainda não há campos cadastrados."}
				buttonText="Cadastrar novo campo"
				onAction={onNew}
				icon={ComponentIcon}
				subject={INSPECTION_SUBJECTS.INSPECTION_FIELDS}
			/>
		);
	}

	return (
		<div className="flex-1 space-y-4">
			<div className="bg-background rounded-controls overflow-x-auto border">
				<Table className="table-fixed">
					<TableHeader className="bg-primary sticky top-0 z-10">
						{table.getHeaderGroups().map(headerGroup => (
							<TableRow key={headerGroup.id}>
								{headerGroup.headers.map(header => {
									const colDef = header.column?.columnDef as unknown as { size?: number } | undefined;
									const style = colDef?.size && header.colSpan === 1 ? { width: `${colDef.size}px` } : undefined;
									return (
										<TableHead
											key={header.id}
											colSpan={header.colSpan}
											style={style}
											className="font-semibold whitespace-nowrap text-white"
										>
											{header.isPlaceholder ? null : flexRender(header.column.columnDef.header, header.getContext())}
										</TableHead>
									);
								})}
							</TableRow>
						))}
					</TableHeader>
					<TableBody>
						{table.getRowModel().rows.map((row, idx) => (
							<TableRow
								key={row.id}
								data-state={row.getIsSelected() && "selected"}
								className={`${idx % 2 === 0 ? "bg-muted/20" : "bg-background"} hover:bg-primary/10 transition-colors`}
							>
								{row.getVisibleCells().map(cell => (
									<TableCell
										key={cell.id}
										className="h-[28px] max-w-[260px] overflow-hidden text-ellipsis whitespace-nowrap"
										title={String(cell.getValue() ?? "")}
									>
										{flexRender(cell.column.columnDef.cell, cell.getContext())}
									</TableCell>
								))}
							</TableRow>
						))}
					</TableBody>
				</Table>
			</div>
			{pagination && (
				<div className="mt-4">
					<Pagination
						currentPage={pagination.currentPage}
						totalPages={pagination.totalPages}
						pageSize={pagination.itemsPerPage}
						totalItems={pagination.totalItems}
						selectedCount={selectedCount}
						onPageChange={setCurrentPage}
						onPageSizeChange={size => setItemsPerPage(size)}
						showPageSizeSelector
						showSelectedInfo
					/>
				</div>
			)}
		</div>
	);
}
