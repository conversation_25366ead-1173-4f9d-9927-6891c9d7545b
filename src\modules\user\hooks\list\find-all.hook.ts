import { usePermissions } from "@/shared/hooks/permissions/permissions.hook";
import { processQueryResponsePaginated } from "@/shared/lib/query/process-query-response";
import { IBaseHookPaginatedReturn } from "@/shared/types/hooks/hook-paginated.type";
import { IPaginationParameters } from "@/shared/types/pagination/types";
import { useQuery } from "@tanstack/react-query";
import { createGetRequest } from "../../../../shared/lib/requests";
import { IResponsePaginated } from "../../../../shared/types/requests/response-paginated.type";
import { USER_ENDPOINTS } from "../../api/endpoints";
import { userQueryKeys } from "../../constants/query";
import { USER_SUBJECTS } from "../../constants/subjects";
import { IUserDto } from "../../types/find-all.dto";

export const useFindAllUser = ({ page, limit, search }: IPaginationParameters): IBaseHookPaginatedReturn<IUserDto> => {
	const { canRead } = usePermissions();

	const { data, isLoading, isFetched } = useQuery({
		queryKey: userQueryKeys.list({ page, limit, search }),
		queryFn: () => createGetRequest<IResponsePaginated<IUserDto>>(USER_ENDPOINTS.FIND_ALL({ page, limit, search })),
		enabled: canRead(USER_SUBJECTS.USER),
	});

	return {
		...processQueryResponsePaginated(data, isFetched),
		isLoading,
	};
};
