"use client";

import { TPermissionSubject } from "@/config/permissions";
import { Button } from "@/shared/components/shadcn/button";
import { cn } from "@/shared/lib/shadcn/utils";
import { motion } from "framer-motion";
import { FileText, Plus } from "lucide-react";
import { ComponentType } from "react";
import { ProtectedComponent } from "../../auth/protected";

interface GenericEmptyStateProps {
	title: string;
	description: string;
	buttonText: string;
	onAction: () => void;
	icon?: ComponentType<{ className?: string }>;
	className?: string;
	subject?: TPermissionSubject;
}

export const GenericEmptyState = ({ title, description, buttonText, onAction, icon: Icon = FileText, className, subject }: GenericEmptyStateProps) => {
	return (
		<div
			className={cn(
				"border-border bg-muted/30 rounded-main min-h0px] flex h-full flex-col items-center justify-center gap-6 border border-dashed p-10 text-center",
				className,
			)}
		>
			<motion.div
				initial={{ opacity: 0, y: 20 }}
				animate={{ opacity: 1, y: 0 }}
				transition={{ duration: 0.5, ease: "easeOut" }}
				className="mx-auto flex max-w-md flex-col items-center px-6 text-center"
			>
				<motion.div
					initial={{ scale: 0.8, opacity: 0 }}
					animate={{ scale: 1, opacity: 1 }}
					transition={{ duration: 0.6, delay: 0.1, ease: "easeOut" }}
					className="mb-8 flex h-20 w-20 items-center justify-center rounded-full bg-gray-100 dark:bg-gray-800"
				>
					<Icon className="h-10 w-10 text-gray-400 dark:text-gray-500" />
				</motion.div>
				<motion.h3
					initial={{ opacity: 0, y: 10 }}
					animate={{ opacity: 1, y: 0 }}
					transition={{ duration: 0.5, delay: 0.2 }}
					className="mb-3 text-xl font-semibold text-gray-900 dark:text-gray-100"
				>
					{title}
				</motion.h3>
				<motion.p
					initial={{ opacity: 0, y: 10 }}
					animate={{ opacity: 1, y: 0 }}
					transition={{ duration: 0.5, delay: 0.3 }}
					className="mb-8 text-sm leading-relaxed text-gray-600 dark:text-gray-400"
				>
					{description}
				</motion.p>

				<motion.div initial={{ opacity: 0, y: 10 }} animate={{ opacity: 1, y: 0 }} transition={{ duration: 0.5, delay: 0.4 }}>
					{subject ? (
						<ProtectedComponent action="create" subject={subject}>
							<Button
								onClick={onAction}
								className="bg-primary hover:bg-primary/90 focus:ring-primary/20 inline-flex items-center gap-2 rounded-lg px-5 py-2.5 text-sm font-medium text-white shadow-sm transition-all duration-200 hover:shadow-md focus:ring-2 focus:ring-offset-2 focus:outline-none"
							>
								<Plus className="h-4 w-4" />
								{buttonText}
							</Button>
						</ProtectedComponent>
					) : (
						<Button
							onClick={onAction}
							className="bg-primary hover:bg-primary/90 focus:ring-primary/20 inline-flex items-center gap-2 rounded-lg px-5 py-2.5 text-sm font-medium text-white shadow-sm transition-all duration-200 hover:shadow-md focus:ring-2 focus:ring-offset-2 focus:outline-none"
						>
							<Plus className="h-4 w-4" />
							{buttonText}
						</Button>
					)}
				</motion.div>
			</motion.div>
		</div>
	);
};
