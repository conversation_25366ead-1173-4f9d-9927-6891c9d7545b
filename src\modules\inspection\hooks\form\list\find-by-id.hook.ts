"use client";
import { INSPECTION_FORM_ENDPOINTS } from "@/modules/inspection/api/endpoints";
import { INSPECTION_SUBJECTS } from "@/modules/inspection/constants/permissions/subjects";
import { IFormFindByIdDto } from "@/modules/inspection/types/forms/dtos/find-by-id.dto";
import { usePermissions } from "@/shared/hooks/permissions/permissions.hook";
import { processQueryById } from "@/shared/lib/query/process-query-by-id";
import { createGetRequest } from "@/shared/lib/requests";
import { IBaseHookFindByIdReturn } from "@/shared/types/hooks/hook-find-by-id.type";
import { useQuery } from "@tanstack/react-query";
import { inspectionKeys } from "../../../constants/query/keys";

export const useFormFindById = (formId: string, enabled: boolean): IBaseHookFindByIdReturn<IFormFindByIdDto> => {
	const { canRead } = usePermissions();

	const { data, isLoading, isFetched } = useQuery({
		queryKey: inspectionKeys.forms.detail(formId),
		queryFn: () => createGetRequest<IFormFindByIdDto>(INSPECTION_FORM_ENDPOINTS.FIND_BY_ID(formId)),
		enabled: canRead(INSPECTION_SUBJECTS.INSPECTION_FORM) && enabled,
	});

	return {
		...processQueryById(data, isFetched),
		isLoading,
	};
};
