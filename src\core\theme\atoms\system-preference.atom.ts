"use client";

import { atom } from "jotai";

const PREFERS_DARK_QUERY = "(prefers-color-scheme: dark)";

const getInitialPreference = (): "light" | "dark" => {
	if (typeof window === "undefined" || typeof window.matchMedia !== "function") return "light";
	return window.matchMedia(PREFERS_DARK_QUERY).matches ? "dark" : "light";
};

export const systemPreferenceAtom = atom<"light" | "dark">(getInitialPreference);
