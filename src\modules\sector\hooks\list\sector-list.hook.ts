"use client";

import { useIsMobile } from "@/shared/hooks/shadcn/use-mobile";
import { usePagination } from "@/shared/hooks/utils";
import { useFindAllSector } from "./find-all.hook";

interface UseSectorProps {
	searchTerm: string;
}

export const useTableSector = ({ searchTerm }: UseSectorProps) => {
	const { currentPage, setCurrentPage, pageSize, setItemsPerPage } = usePagination();
	const isMobile = useIsMobile();

	const { data, isLoading, error, pagination, hasError, isEmpty } = useFindAllSector({
		limit: pageSize,
		page: currentPage,
		search: searchTerm || "",
	});

	const handlePageSizeChange = (size: number) => {
		setItemsPerPage(size);
		setCurrentPage(1);
	};

	return {
		data,
		isLoading,
		isEmpty,
		error,
		hasError,
		pagination,
		pageSize,
		isMobile,
		handlePageSizeChange,
		setCurrentPage,
	};
};
