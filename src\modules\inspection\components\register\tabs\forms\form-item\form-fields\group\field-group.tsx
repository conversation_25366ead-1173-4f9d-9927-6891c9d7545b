import { addItemToGroup<PERSON>tom } from "@/modules/inspection/atoms/forms/fields/field-actions.atom";
import { removeFieldGroupAtom } from "@/modules/inspection/atoms/forms/fields/group-actions.atom";
import { IFieldGroup } from "@/modules/inspection/types/forms/fields-table/fields-group.type";
import { ICreateFieldForm } from "@/modules/inspection/validators/form/create-field";
import { TableCell, TableRow } from "@/shared/components/shadcn/table";
import { useSortable } from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import { ColumnDef } from "@tanstack/react-table";
import { useSet<PERSON>tom } from "jotai";
import React from "react";
import { ItemsFieldGroup } from "../items/items-group";
import { GroupDragContext } from "./drag-context";
import { GroupHeader } from "./group-header";

interface IGroupHeaderProps {
	group: IFieldGroup;
	colSpan: number;
	columns: ColumnDef<ICreateFieldForm>[];
	isOverlay?: boolean;
	mode: "create" | "edit" | "view";
}

export const GroupItem: React.FC<IGroupHeaderProps & { autoFocus?: boolean }> = ({ group, colSpan, columns, autoFocus, isOverlay, mode = "create" }) => {
	const addItemToGroup = useSetAtom(addItemToGroupAtom);
	const removeGroup = useSetAtom(removeFieldGroupAtom);
	const { attributes, listeners, setNodeRef, transform, transition, isDragging } = useSortable({
		id: group.tempId,
		data: {
			type: "group",
			group,
		},
	});

	if (group.tempId.startsWith("empty")) {
		return (
			<React.Fragment>
				<TableRow
					ref={setNodeRef}
					data-dragging={isDragging}
					style={{
						transform: CSS.Transform.toString(transform),
						transition: transition || "transform 200ms ease",
						opacity: isDragging ? 0.5 : 1,
					}}
					className={`sortable-item relative ${isDragging ? "z-10" : "z-0"}`}
				>
					<TableCell colSpan={colSpan} className="p-0">
						{group.items.length > 0 && (
							<GroupDragContext.Provider
								value={{
									groupDragProps: { attributes, listeners },
									isOnlyItem: group.items.length === 1,
								}}
							>
								<div className="hover:border-primary/50 transition-colors">
									<ItemsFieldGroup isOverlay={isOverlay ?? false} items={group.items} groupId={group.tempId} columns={columns} mode={mode} />
								</div>
							</GroupDragContext.Provider>
						)}
					</TableCell>
				</TableRow>
			</React.Fragment>
		);
	}

	return (
		<React.Fragment>
			<TableRow
				ref={setNodeRef}
				data-dragging={isDragging}
				style={{
					transform: CSS.Transform.toString(transform),
					transition: transition || "transform 200ms ease",
					opacity: isDragging ? 0.5 : 1,
				}}
				className={`sortable-item relative ${isDragging ? "z-10" : "z-0"}`}
			>
				<TableCell colSpan={colSpan} className="p-0">
					<div className={`border-primary/40 overflow-hidden border shadow-sm ${isDragging ? "bg-primary/5 opacity-70" : ""}`}>
						<GroupHeader
							group={group}
							onAddItem={() => addItemToGroup(group.tempId)}
							onRemoveGroup={() => removeGroup(group.tempId)}
							dragProps={{ attributes, listeners }}
							autoFocus={autoFocus}
							mode={mode}
						/>
						{group.items.length > 0 ? (
							<GroupDragContext.Provider
								value={{
									groupDragProps: { attributes, listeners },
									isOnlyItem: group.items.length === 1,
								}}
							>
								<div className="bg-primary/50">
									<ItemsFieldGroup isOverlay={isOverlay ?? false} items={group.items} groupId={group.tempId} columns={columns} mode={mode} />
								</div>
							</GroupDragContext.Provider>
						) : (
							<div className="bg-background/80 text-text-secondary p-4 text-center italic">
								Nenhum campo adicionado ao grupo. Clique em &quot;Adicionar campo&quot; para começar.
							</div>
						)}
					</div>
				</TableCell>
			</TableRow>
		</React.Fragment>
	);
};
