"use client";

import { RegisterInspectionTabs } from "../components/register/register-inspection-tabs";

export const RegisterInspectionScreen = () => {
	return (
		<div className="container mx-auto flex h-full min-h-full flex-1 flex-col space-y-6 px-4 py-1 md:px-2">
			{/* <header className="mb-4 flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
				<div className="flex w-full items-center gap-4">
					<div className="bg-muted text-primary border-primary flex h-10 w-10 items-center justify-center rounded-lg border">
						{Icon ? <Icon /> : null}
					</div>
					<div className="flex w-full items-center">
						<h1 className="flex-shrink-0 text-2xl font-semibold whitespace-nowrap text-gray-900">Cadastros de inspeção</h1>
					</div>
				</div>
				<div className="flex gap-3"></div>
			</header> */}

			<RegisterInspectionTabs />
		</div>
	);
};
