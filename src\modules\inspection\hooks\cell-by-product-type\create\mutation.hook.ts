import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "../../../../../core/toast";
import { createPostRequest } from "../../../../../shared/lib/requests";
import { IMessageGlobalReturn } from "../../../../../shared/types/requests/message.type";
import { CELL_BY_PRODUCT_TYPE_ENDPOINTS } from "../../../api/endpoints";
import { inspectionKeys } from "../../../constants/query/keys";
import { ICreateCellByProductTypeDto } from "../../../types/cell-by-product-type/dtos/create.dto";

export const useCreateCellByProductTypeMutation = (onClose: () => void) => {
	const queryClient = useQueryClient();

	const createFormMutation = useMutation({
		mutationKey: inspectionKeys.cellByProductType.custom("create"),
		mutationFn: async (formData: ICreateCellByProductTypeDto) => {
			const res = await createPostRequest<IMessageGlobalReturn>(CELL_BY_PRODUCT_TYPE_ENDPOINTS.CREATE, formData);
			if (!res.success) throw new Error(res.data.message);
			return res.data;
		},
		onSuccess: () => {
			inspectionKeys.cellByProductType.invalidateAllLists(queryClient);
			onClose();
		},
	});

	return {
		createCellByProductType: (formData: ICreateCellByProductTypeDto) =>
			toast.promise(createFormMutation.mutateAsync(formData), {
				loading: "Criando tipo de produto da célula...",
				success: ({ message }) => message ?? "Tipo de produto da célula criado com sucesso!",
				error: error => error.message || "Erro ao criar tipo de produto da célula",
			}),
	};
};
