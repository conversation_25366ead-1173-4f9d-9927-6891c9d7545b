import { TableLoading } from "@/shared/components/custom/loading";
import { Pagination } from "@/shared/components/custom/pagination";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/shared/components/shadcn/table";
import { flexRender, getCoreRowModel, useReactTable } from "@tanstack/react-table";
import { IComponentTypesDto } from "../../types/find-all.dto";
import { componentTypeColumns } from "./columns";

interface DesktopTableViewProps {
	data: IComponentTypesDto[] | undefined;
	isLoading: boolean;
	hasError: boolean;
	error: string | false | undefined;
	pageSize: number;
	pagination: {
		currentPage: number;
		totalPages: number;
		itemsPerPage: number;
		totalItems: number;
	} | null;
	onPageChange: (page: number) => void;
	onPageSizeChange: (size: number) => void;
}

export const ComponentTypeDesktopTableView = ({
	data,
	isLoading,
	hasError,
	error,
	pageSize,
	pagination,
	onPageChange,
	onPageSizeChange,
}: DesktopTableViewProps) => {
	const table = useReactTable({
		data: data ?? [],
		columns: componentTypeColumns,
		getCoreRowModel: getCoreRowModel(),
		manualPagination: true,
		pageCount: pagination?.totalPages ?? 0,
	});

	const renderTableBody = () => {
		if (hasError) {
			return (
				<TableRow>
					<TableCell colSpan={componentTypeColumns.length} className="h-24 text-center text-red-500">
						Erro ao carregar os dados: {error || "Ocorreu um erro desconhecido."}
					</TableCell>
				</TableRow>
			);
		}

		if (isLoading) {
			return <TableLoading columns={componentTypeColumns.length} rows={pageSize} />;
		}

		if (table.getRowModel().rows.length) {
			return table.getRowModel().rows.map(row => {
				return (
					<TableRow key={row.id} data-state={row.getIsSelected() && "selected"} className={"!text-text-primary transition-colors"}>
						{row.getVisibleCells().map(cell => (
							<TableCell key={cell.id} className="px-4 text-center align-middle" title={String(cell.getValue() ?? "")}>
								{flexRender(cell.column.columnDef.cell, cell.getContext())}
							</TableCell>
						))}
					</TableRow>
				);
			});
		}
	};

	return (
		<div className="rounded-main flex flex-col space-y-4 text-center">
			<div className="bg-background rounded-controls flex-1 overflow-x-auto border">
				<Table className="table-fixed">
					<TableHeader className="from-primary to-primary/95 supports-[backdrop-filter]:bg-primary/80 sticky top-0 z-10 bg-gradient-to-r backdrop-blur">
						{table.getHeaderGroups().map(headerGroup => (
							<TableRow key={headerGroup.id}>
								{headerGroup.headers.map(header => {
									const colDef = header.column?.columnDef as unknown as { size?: number } | undefined;
									const style = colDef?.size && header.colSpan === 1 ? { width: `${colDef.size}px` } : undefined;
									return (
										<TableHead
											key={header.id}
											colSpan={header.colSpan}
											style={style}
											className="font-semibold whitespace-nowrap text-white"
										>
											{header.isPlaceholder ? null : flexRender(header.column.columnDef.header, header.getContext())}
										</TableHead>
									);
								})}
							</TableRow>
						))}
					</TableHeader>
					<TableBody>{renderTableBody()}</TableBody>
				</Table>
			</div>
			{pagination && (
				<Pagination
					currentPage={pagination.currentPage}
					totalPages={pagination.totalPages}
					pageSize={pagination.itemsPerPage}
					totalItems={pagination.totalItems}
					onPageChange={onPageChange}
					onPageSizeChange={size => {
						onPageSizeChange(size);
						onPageChange(1);
					}}
					showPageSizeSelector
					showSelectedInfo
				/>
			)}
		</div>
	);
};
