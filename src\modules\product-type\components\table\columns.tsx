import { ColumnDef } from "@tanstack/react-table";
import { IProductTypesDto } from "../../types/find-all.dto";
import { ProductTypeActions } from "./actions";

export const productTypeColumns: ColumnDef<IProductTypesDto>[] = [
	{
		accessorKey: "name",
		header: () => <div className="pl-[10px] text-start font-semibold">Nome</div>,
		cell: ({ row }) => (
			<div className="flex items-center justify-start pl-[10px]">
				<div className="max-w-[300px]">
					<span className="text-text-primary block truncate font-medium" title={row.original.name}>
						{row.original.name}
					</span>
				</div>
			</div>
		),
		size: 300,
	},
	{
		accessorKey: "factoryStatus",
		header: () => <div className="text-center font-semibold">Status fábrica</div>,
		cell: ({ row }) => (
			<div className="flex justify-center">
				<span className="text-text-primary text-sm font-medium">{row.original.factoryStatus ? "Habilitado" : "Desabilitado"}</span>
			</div>
		),
		size: 120,
	},
	{
		accessorKey: "businessStatus",
		header: () => <div className="text-center font-semibold">Status comercial</div>,
		cell: ({ row }) => (
			<div className="flex justify-center">
				<span className="text-text-primary text-sm font-medium">{row.original.businessStatus ? "Habilitado" : "Desabilitado"}</span>
			</div>
		),
		size: 120,
	},
	{
		id: "actions",
		header: () => <div className="pr-[10px] text-right font-semibold">Ações</div>,
		cell: ({ row }) => <ProductTypeActions productTypeId={String(row.original.id)} name={row.original.name} />,
		size: 80,
	},
];
