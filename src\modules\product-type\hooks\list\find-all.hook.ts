"use client";

import { usePermissions } from "@/shared/hooks/permissions/permissions.hook";
import { processQueryResponsePaginated } from "@/shared/lib/query/process-query-response";
import { createGetRequest } from "@/shared/lib/requests";
import { IBaseHookPaginatedReturn } from "@/shared/types/hooks/hook-paginated.type";
import { IPaginationParameters } from "@/shared/types/pagination/types";
import { IResponsePaginated } from "@/shared/types/requests/response-paginated.type";
import { useQuery } from "@tanstack/react-query";
import { PRODUCT_TYPE_ENDPOINTS } from "../../api/endpoints";
import { productTypeKeys } from "../../constants/query";
import { PRODUCT_TYPE_SUBJECTS } from "../../constants/subjects";
import { IProductTypesDto } from "../../types/find-all.dto";

export const useFindAllProductTypes = (params: IPaginationParameters): IBaseHookPaginatedReturn<IProductTypesDto> => {
	const { canRead } = usePermissions();

	const { data, isLoading, isFetched } = useQuery({
		queryKey: productTypeKeys.list({
			...params,
		}),
		queryFn: () => createGetRequest<IResponsePaginated<IProductTypesDto>>(PRODUCT_TYPE_ENDPOINTS.FIND_ALL(params)),
		enabled: canRead(PRODUCT_TYPE_SUBJECTS.PRODUCT_TYPE),
	});

	return {
		...processQueryResponsePaginated(data, isFetched),
		isLoading,
	};
};
