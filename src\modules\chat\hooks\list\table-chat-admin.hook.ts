"use client";

import { useFindAllChatKnowledge } from "@/modules/chat/hooks/list/find-all.hook";
import { usePermissions } from "@/shared/hooks/permissions/permissions.hook";
import { useIsMobile } from "@/shared/hooks/shadcn/use-mobile";
import { usePagination } from "@/shared/hooks/utils";
import { useEditKnowledge } from "./edit-knowledge.hook";

interface UseTableChatAdminProps {
	searchTerm: string;
}

export const useTableChatAdmin = ({ searchTerm }: UseTableChatAdminProps) => {
	const { currentPage, setCurrentPage, pageSize, setItemsPerPage } = usePagination();
	const isMobile = useIsMobile();
	const editKnowledge = useEditKnowledge();
	const { canUpdate } = usePermissions();

	const { data, isLoading, error, pagination, hasError, isEmpty } = useFindAllChatKnowledge({
		limit: pageSize,
		page: currentPage,
		search: searchTerm || "",
	});

	const handleRowClick = (id: string) => {
		editKnowledge.openEditModal(id);
	};

	const handlePageSizeChange = (size: number) => {
		setItemsPerPage(size);
		setCurrentPage(1);
	};

	return {
		data,
		isLoading,
		isEmpty,
		error,
		hasError,
		pagination,
		pageSize,
		isMobile,
		canUpdate: canUpdate("all"),
		editKnowledge,
		handleRowClick,
		handlePageSizeChange,
		setCurrentPage,
	};
};
