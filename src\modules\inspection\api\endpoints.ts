import { buildQueryParams } from "@/shared/lib/utils/url-query-params";
import { IPaginationParameters } from "@/shared/types/pagination/types";

const BASE = "/inspection";
const BASE_FORM = `${BASE}/forms`;
const BASE_FORM_LINK = `${BASE}/forms/links`;
const BASE_MEASURE = `${BASE}/measures`;
const BASE_FIELDS = `${BASE}/fields`;
const BASE_CELL_COMPONENTS = `${BASE}/cell-production-by-components`;
const BASE_CELL_COMPONENTS_DELETE = `${BASE}/cell-production-by-component`;
const BASE_CELL_BY_PRODUCT = `${BASE}/cell-production-by-product-type`;
const BASE_COLLAB_BY_SECTOR = `${BASE}/collaborator-by-sector`;

const makeCrud = (base: string, deleteBase?: string) =>
	Object.freeze({
		CREATE: base,
		FIND_ALL: (params?: IPaginationParameters) => buildQueryParams(base, { ...params }),
		DELETE: (id: string) => `${deleteBase ?? base}/${encodeURIComponent(id)}`,
	});

const makeForm = (base: string) => {
	const withId = (id: string) => `${base}/${encodeURIComponent(id)}`;
	return Object.freeze({
		CREATE: base,
		FIND_ALL: (params?: IPaginationParameters) => buildQueryParams(base, { ...params }),
		UPDATE: (id: string) => withId(id),
		DELETE: (id: string) => withId(id),
		CLONE: (id: string) => `${withId(id)}/clone`,
		FIND_BY_ID: (id: string) => withId(id),
	});
};

export const INSPECTION_FORM_ENDPOINTS = makeForm(BASE_FORM);
export const INSPECTION_FORMS_LINKS_ENDPOINTS = makeCrud(BASE_FORM_LINK);
export const MEASURES_ENDPOINTS = makeCrud(BASE_MEASURE);
export const FIELDS_ENDPOINTS = makeCrud(BASE_FIELDS);
export const CELL_COMPONENTS_ENDPOINTS = makeCrud(BASE_CELL_COMPONENTS, BASE_CELL_COMPONENTS_DELETE);
export const CELL_BY_PRODUCT_TYPE_ENDPOINTS = makeCrud(BASE_CELL_BY_PRODUCT);
export const COLLAB_BY_SECTOR_ENDPOINTS = makeCrud(BASE_COLLAB_BY_SECTOR);
