import { IFieldDto } from "@/modules/inspection/hooks/fields/list/find-all.hook";
import { ColumnDef } from "@tanstack/react-table";
import { FieldsListActions } from "./actions";

export const columnsFields: ColumnDef<IFieldDto>[] = [
	{
		accessorKey: "nome",
		header: () => <div className="pl-[10px] text-start font-semibold">Nome</div>,
		cell: ({ row }) => (
			<div className="text-start">
				<span className="text-text-primary block max-w-[200px] truncate pl-[10px] font-medium">{row.original.name}</span>
			</div>
		),
	},
	{
		id: "actions",
		header: () => <div className="pr-[10px] text-right font-semibold">Ações</div>,
		cell: ({ row }) => <FieldsListActions fieldsId={row.original.id} name={row.original.name} />,
		enableSorting: false,
		enableHiding: false,
		size: 80,
	},
];
