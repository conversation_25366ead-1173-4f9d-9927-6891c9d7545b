import { INSPECTION_SUBJECTS } from "@/modules/inspection/constants/permissions/subjects";
import { ProtectedComponent } from "@/shared/components/auth/protected";
import { Trash } from "lucide-react";
import { Button } from "../../../../../../../shared/components/shadcn/button";
import { useModal } from "../../../../../../../shared/hooks/utils/modal.hook";
import { ConfirmDeleteCollabBySectorTypeModal } from "../delete/modal";

export const CollabBySectorActions = ({ id, name }: { id: string; name: string }) => {
	const deleteModal = useModal();

	return (
		<div className="text-right">
			<ProtectedComponent action="delete" subject={INSPECTION_SUBJECTS.INSPECTION_COLLABORATOR_BY_SECTOR}>
				<Button size="icon" onClick={deleteModal.openModal} className="group ml-2 h-8 w-8 bg-red-500/5 hover:bg-red-500/30">
					<Trash className="h-4 w-4 text-red-500 group-hover:text-white" />
				</Button>
			</ProtectedComponent>
			<ConfirmDeleteCollabBySectorTypeModal isOpen={deleteModal.isOpen} onClose={deleteModal.closeModal} name={name} id={id} />
		</div>
	);
};
