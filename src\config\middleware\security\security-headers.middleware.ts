export class SecurityHeadersMiddleware {
	private static buildCsp(nonce: string | undefined, isDev: boolean): string {
		const self = "'self'";
		const connectExtra = [process.env.BACKEND_URL];
		const nonceDirective = nonce ? ` 'nonce-${nonce}'` : "";
		const scriptSrc = [self, nonceDirective.trim(), isDev ? "'unsafe-eval'" : ""].filter(Boolean).join(" ");
		const styleSrc = [self, "'unsafe-inline'"].join(" ");
		const connectSrc = [self, ...connectExtra].join(" ");
		const fontSrc = [self, "data:", "https://fonts.gstatic.com"].join(" ");
		const imgSrc = [self, "data:", "blob:"].join(" ");
		const securityHeaderFrameOrigin = "'none'";
		const baseUri = self;

		return [
			`default-src ${self}`,
			`script-src ${scriptSrc}`,
			`style-src ${styleSrc}`,
			`connect-src ${connectSrc}`,
			`img-src ${imgSrc}`,
			`font-src ${fontSrc}`,
			"object-src 'none'",
			"form-action 'self'",
			`base-uri ${baseUri}`,
			`frame-ancestors ${securityHeaderFrameOrigin}`,
			"upgrade-insecure-requests",
		].join("; ");
	}

	static getBasicHeaders(nonce?: string): Record<string, string> {
		const isDev = process.env.NODE_ENV === "development";
		return {
			"Strict-Transport-Security": "max-age=31536000; includeSubDomains; preload",
			"X-Content-Type-Options": "nosniff",
			"X-Frame-Options": "DENY",
			"Content-Security-Policy": this.buildCsp(nonce, isDev),
			"X-XSS-Protection": "0",
			"Referrer-Policy": "strict-origin-when-cross-origin",
			"Permissions-Policy": "camera=(), microphone=(), geolocation=(), interest-cohort=()",
			"Cross-Origin-Opener-Policy": "same-origin",
			"Cross-Origin-Embedder-Policy": "require-corp",
			"Cross-Origin-Resource-Policy": "same-origin",
		};
	}

	static addToResponse(response: Response, nonce?: string): Response {
		const newHeaders = new Headers(response.headers);
		const securityHeaders = this.getBasicHeaders(nonce);
		for (const [key, value] of Object.entries(securityHeaders)) newHeaders.set(key, value);
		return new Response(response.body, {
			status: response.status,
			statusText: response.statusText,
			headers: newHeaders,
		});
	}
}
