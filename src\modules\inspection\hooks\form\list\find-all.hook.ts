"use client";
import { INSPECTION_FORM_ENDPOINTS } from "@/modules/inspection/api/endpoints";
import { INSPECTION_SUBJECTS } from "@/modules/inspection/constants/permissions/subjects";
import { usePermissions } from "@/shared/hooks/permissions/permissions.hook";
import { processQueryResponsePaginated } from "@/shared/lib/query/process-query-response";
import { createGetRequest } from "@/shared/lib/requests";
import { IBaseHookPaginatedReturn } from "@/shared/types/hooks/hook-paginated.type";
import { IResponsePaginated } from "@/shared/types/requests/response-paginated.type";
import { useQuery } from "@tanstack/react-query";
import { IPaginationParameters } from "../../../../../shared/types/pagination/types";
import { inspectionKeys } from "../../../constants/query/keys";
import { IFormDto } from "../../../types/forms/dtos/find-all.dto";

export const useFindAllForms = ({ page = 1, limit = 10, search = "" }: IPaginationParameters = {}): IBaseHookPaginatedReturn<IFormDto> => {
	const { canRead } = usePermissions();

	const { data, isLoading, isFetched } = useQuery({
		queryKey: inspectionKeys.forms.list({ page, limit, search }),
		queryFn: () => createGetRequest<IResponsePaginated<IFormDto>>(INSPECTION_FORM_ENDPOINTS.FIND_ALL({ page, limit, search })),
		enabled: canRead(INSPECTION_SUBJECTS.INSPECTION_FORM),
	});

	return {
		...processQueryResponsePaginated(data, isFetched),
		isLoading,
	};
};
