"use client";
import { SUBJECTS } from "@/config/permissions";
import { usePermissions } from "@/shared/hooks/permissions/permissions.hook";
import { processQueryResponsePaginated } from "@/shared/lib/query/process-query-response";
import { IBaseHookPaginatedReturn } from "@/shared/types/hooks/hook-paginated.type";
import { IPaginationParameters } from "@/shared/types/pagination/types";
import { useQuery } from "@tanstack/react-query";
import { createGetRequest } from "../../../../shared/lib/requests";
import { IResponsePaginated } from "../../../../shared/types/requests/response-paginated.type";
import { ACTIVITY_ENDPOINTS } from "../../api/endpoints";
import { activityQueryKeys } from "../../constants/query";
import { IActivityDto } from "../../types/find-all.dto";

export const useFindAllActivity = ({ page, limit, search }: IPaginationParameters): IBaseHookPaginatedReturn<IActivityDto> => {
	const { canRead } = usePermissions();

	const { data, isLoading, isFetched } = useQuery({
		queryKey: activityQueryKeys.list({ page, limit, search }),
		queryFn: () => createGetRequest<IResponsePaginated<IActivityDto>>(ACTIVITY_ENDPOINTS.FIND_ALL({ page, limit, search })),
		enabled: canRead(SUBJECTS.ACTIVITY),
	});

	return {
		...processQueryResponsePaginated(data, isFetched),
		isLoading,
	};
};
