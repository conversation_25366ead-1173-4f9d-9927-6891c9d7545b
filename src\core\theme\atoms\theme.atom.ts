"use client";

import { atom } from "jotai";
import { atomWithStorage } from "jotai/utils";
import { THEME_PERSISTENCE_CONFIG } from "../constants/preferences";
import { isThemeVariant as _isThemeVariant, THEME_VARIANTS as _THEME_VARIANTS, ThemeVariant as _ThemeVariant, themeCookieStorage } from "../lib/cookie-storage";

export type ThemeVariant = _ThemeVariant;
export const THEME_VARIANTS = _THEME_VARIANTS;
export const isThemeVariant = _isThemeVariant;

export const themeAtom = atomWithStorage<_ThemeVariant>(THEME_PERSISTENCE_CONFIG.cookieName, "light", themeCookieStorage, { getOnInit: true });

export const userExplicitThemeAtom = atom<boolean>(false);

export const getCurrentTheme = atom(get => get(themeAtom));

export const applyTheme = atom(null, (get, set, newTheme: ThemeVariant | null = null, options?: { explicit?: boolean }) => {
	const theme = newTheme ?? get(themeAtom);
	if (!isThemeVariant(theme)) return;
	document.documentElement.classList.remove(...THEME_VARIANTS);
	document.documentElement.classList.add(theme);
	set(themeAtom, theme);
	if (options?.explicit) set(userExplicitThemeAtom, true);
});

applyTheme.onMount = initialize => initialize();

export const mountedAtom = atom(false);
mountedAtom.onMount = set => set(true);
