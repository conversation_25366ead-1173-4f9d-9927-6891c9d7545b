import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { createCellComponentSchema, TCreateCellComponentTypeForm } from "../../../validators/cell-components/create";

export const useCreateCellComponentForm = () => {
	return useForm<TCreateCellComponentTypeForm>({
		resolver: zodResolver(createCellComponentSchema),
		defaultValues: {
			cell: undefined,
			component: undefined,
		},
	});
};
