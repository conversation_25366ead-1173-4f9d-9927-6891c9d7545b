"use client";

import { useDynamicBreadcrumb } from "@/layout/hooks/dynamic-breadcrumb.hook";
import {
	Breadcrumb,
	BreadcrumbE<PERSON>psis,
	BreadcrumbItem,
	BreadcrumbLink,
	BreadcrumbList,
	BreadcrumbPage,
	BreadcrumbSeparator,
} from "@/shared/components/shadcn/breadcrumb";
import Link from "next/link";
import { Fragment, JSX, useState, type FC } from "react";

export const DynamicBreadcrumb: FC = (): JSX.Element | undefined => {
	const { uniqueBreadcrumbPath } = useDynamicBreadcrumb();
	const [isExpanded, setIsExpanded] = useState(false);

	const shouldCollapse = uniqueBreadcrumbPath.length > 3;
	const visibleItems = shouldCollapse && !isExpanded ? [uniqueBreadcrumbPath[0], ...uniqueBreadcrumbPath.slice(-2)] : uniqueBreadcrumbPath;

	const hasHiddenItems = shouldCollapse && !isExpanded && uniqueBreadcrumbPath.length > 3;

	return (
		<div className="flex flex-col">
			<Breadcrumb>
				<BreadcrumbList className="flex-wrap">
					{visibleItems.map((item, idx) => {
						const actualIdx = shouldCollapse && !isExpanded ? (idx === 0 ? 0 : uniqueBreadcrumbPath.length - (visibleItems.length - idx)) : idx;

						return (
							<Fragment key={`${item.label}-${actualIdx}`}>
								{hasHiddenItems && idx === 1 && (
									<BreadcrumbItem>
										<BreadcrumbEllipsis
											className="hover:bg-accent cursor-pointer p-1 transition-colors hover:rounded-md"
											onClick={() => setIsExpanded(true)}
											aria-label="Expand breadcrumb"
										/>
									</BreadcrumbItem>
								)}
								{hasHiddenItems && idx === 1 ? null : (
									<BreadcrumbItem>
										{actualIdx === uniqueBreadcrumbPath.length - 1 ? (
											<BreadcrumbPage className="text-text-primary max-w-[120px] truncate text-sm font-medium sm:max-w-none sm:text-base">
												{item.label}
											</BreadcrumbPage>
										) : (
											<>
												{item.isActive ? (
													<BreadcrumbLink
														className="text-text-secondary hover:text-text-primary max-w-[100px] truncate text-sm font-light transition-colors sm:max-w-none sm:text-base"
														asChild
													>
														<Link href={item.href ?? "#"}>{item.label}</Link>
													</BreadcrumbLink>
												) : (
													<span className="text-text-secondary hover:text-text-primary max-w-[100px] cursor-pointer truncate text-sm font-light transition-colors sm:max-w-none sm:text-base">
														{item.label}
													</span>
												)}
											</>
										)}
									</BreadcrumbItem>
								)}
								{actualIdx < uniqueBreadcrumbPath.length - 1 && (
									<BreadcrumbSeparator className="text-text-secondary text-[13px] sm:text-[15px]" />
								)}
							</Fragment>
						);
					})}
				</BreadcrumbList>
			</Breadcrumb>
			{/* {lastItem.description && <p className="text-sm sm:text-base text-text-secondary/50">{lastItem.description}</p>} */}
		</div>
	);
};
