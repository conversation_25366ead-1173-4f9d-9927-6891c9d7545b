"use client";

import { getUserInitials } from "@/core/auth/atoms/user.atom";
import { useLogout } from "@/core/auth/hooks/logout/logout.hook";
import { useUser } from "@/core/auth/hooks/user/user.hook";
import { useTheme } from "@/core/theme/hooks/use-theme.hook";
import { Avatar, AvatarFallback, AvatarImage } from "@/shared/components/shadcn/avatar";
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuLabel,
	DropdownMenuSeparator,
	DropdownMenuTrigger,
} from "@/shared/components/shadcn/dropdown-menu";
import { SidebarMenu, SidebarMenuButton, SidebarMenuItem } from "@/shared/components/shadcn/sidebar";
import { useAtomValue } from "jotai";
import { Bell, Check, Leaf, LogOut, Moon, MoreVertical, Settings, Sun, User } from "lucide-react";
import { Suspense } from "react";

type ThemeItemProps = { label: string; icon: React.ElementType; active: boolean; onSelect: () => void };

const ThemeItem = ({ label, icon: Icon, active, onSelect }: ThemeItemProps) => (
	<DropdownMenuItem className={`gap-2 pr-2 pl-2 ${active ? "bg-sidebar-accent text-sidebar-accent-foreground" : ""}`} onClick={onSelect}>
		<Icon size={15} />
		<span className="flex-1 text-sm">{label}</span>
		{active && <Check size={14} className="opacity-90" />}
	</DropdownMenuItem>
);

const UserProfileContent = () => {
	const { user, userName } = useUser();
	const userInitials = useAtomValue(getUserInitials);
	const { logout, isLoggingOut } = useLogout();
	const { theme, setLight, setDark, setLightGreen, setDarkGreen } = useTheme();

	return (
		<SidebarMenu>
			<SidebarMenuItem>
				<DropdownMenu>
					<DropdownMenuTrigger asChild>
						<SidebarMenuButton size="lg" className="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground">
							<Avatar className="h-[40px] w-[40px] rounded-lg">
								<AvatarImage src="" alt={userName ?? "Usuário"} />
								<AvatarFallback className="rounded-lg">{userInitials}</AvatarFallback>
							</Avatar>
							<div className="grid flex-1 text-left text-sm leading-tight">
								<span className="text-text truncate font-semibold">{userName ?? "Usuário"}</span>
								<span className="text-text-light truncate text-xs font-light">{user?.roles[0]}</span>
							</div>
							<div className="flex gap-3">
								<Bell className="text-text-light size-4 h-[24px] w-[24px]" />
								<MoreVertical className="text-text-light size-4 h-[24px] w-[24px]" />
							</div>
						</SidebarMenuButton>
					</DropdownMenuTrigger>
					<DropdownMenuContent className="w-[--radix-dropdown-menu-trigger-width] min-w-56 rounded-lg" align="end" sideOffset={4}>
						<DropdownMenuLabel className="p-0 font-normal">
							<div className="flex items-center gap-2 px-1 py-1.5 text-left text-sm">
								<Avatar className="h-8 w-8 rounded-lg">
									<AvatarImage src="" alt={userName ?? "Usuário"} />
									<AvatarFallback className="rounded-lg">{userInitials}</AvatarFallback>
								</Avatar>
								<div className="grid flex-1 text-left text-sm leading-tight">
									<span className="truncate font-semibold">{userName ?? "Usuário"}</span>
									<span className="truncate text-xs">{user?.email ? `${user.email}` : "Sem email"}</span>
								</div>
							</div>
						</DropdownMenuLabel>
						<DropdownMenuSeparator />
						<ThemeItem label="Claro" icon={Sun} active={theme === "light"} onSelect={setLight} />
						<ThemeItem label="Escuro" icon={Moon} active={theme === "dark"} onSelect={setDark} />
						<ThemeItem label="Verde Claro" icon={Leaf} active={theme === "light-green"} onSelect={setLightGreen} />
						<ThemeItem label="Verde Escuro" icon={Leaf} active={theme === "dark-green"} onSelect={setDarkGreen} />
						<DropdownMenuSeparator />
						<DropdownMenuItem className="gap-2">
							<User size={16} />
							<span>Meu Perfil</span>
						</DropdownMenuItem>
						<DropdownMenuItem className="gap-2">
							<Settings size={16} />
							<span>Configurações</span>
						</DropdownMenuItem>
						<DropdownMenuSeparator />
						<DropdownMenuItem className="text-destructive focus:text-destructive gap-2" onClick={logout} disabled={isLoggingOut}>
							<LogOut size={16} />
							<span>{isLoggingOut ? "Saindo..." : "Sair"}</span>
						</DropdownMenuItem>
					</DropdownMenuContent>
				</DropdownMenu>
			</SidebarMenuItem>
		</SidebarMenu>
	);
};

export const UserProfile = () => {
	return (
		<Suspense fallback={<div>Carregando...</div>}>
			<UserProfileContent />
		</Suspense>
	);
};
