"use client";

import { use<PERSON>tom, useSet<PERSON>tom } from "jotai";
import { applyTheme, getCurrentTheme, userExplicitThemeAtom } from "../atoms/theme.atom";
import { isThemeVariant, THEME_VARIANTS, ThemeVariant } from "../lib/cookie-storage";

export interface UseThemeReturn {
	theme: ThemeVariant;
	setTheme: (variant: ThemeVariant, opts?: { explicit?: boolean }) => void;
	toggle: () => void;
	isDark: boolean;
	isLight: boolean;
	available: ThemeVariant[];
	setDark: () => void;
	setLight: () => void;
	setLightGreen: () => void;
	setDarkGreen: () => void;
}

const NEXT_THEME_ORDER: Record<ThemeVariant, ThemeVariant> = {
	light: "dark",
	dark: "light-green",
	"light-green": "dark-green",
	"dark-green": "light",
};

export const useTheme = (): UseThemeReturn => {
	const [theme] = useAtom(getCurrentTheme);
	const setApply = useSetAtom(applyTheme);
	const setExplicit = useSetAtom(userExplicitThemeAtom);

	const setTheme = (variant: ThemeVariant, opts: { explicit?: boolean } = { explicit: true }) => {
		if (!isThemeVariant(variant)) return;
		setApply(variant, { explicit: opts.explicit });
		if (opts.explicit) setExplicit(true);
	};

	const toggle = () => {
		setTheme(NEXT_THEME_ORDER[theme]);
	};

	return {
		theme,
		setTheme,
		toggle,
		isDark: theme === "dark" || theme === "dark-green",
		isLight: theme === "light" || theme === "light-green",
		available: THEME_VARIANTS,
		setDark: () => setTheme("dark"),
		setLight: () => setTheme("light"),
		setLightGreen: () => setTheme("light-green"),
		setDarkGreen: () => setTheme("dark-green"),
	};
};
