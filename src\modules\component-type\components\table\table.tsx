"use client";

import { BotIcon } from "lucide-react";
import { ComponentType, useState } from "react";
import { pathService } from "../../../../config/path-manager/service";
import { MainHeaderComponent } from "../../../../shared/components/custom/admin-header";
import { GenericEmptyState } from "../../../../shared/components/custom/empty";
import { COMPONENT_TYPE_SUBJECTS } from "../../constants/subjects";
import { useTableComponentType } from "../../hooks/list/component-type-table.hook";
import { ComponentTypeDesktopTableView } from "./desktop-table-view";
import { ComponentTypeMobileTableView } from "./mobile-table-view";

interface ITableComponentTypeProps {
	onOpenModal: () => void;
}

export const TableComponentType = ({ onOpenModal }: ITableComponentTypeProps) => {
	const [searchTerm, setSearchTerm] = useState("");
	const item = pathService.getItemById("component-type");
	const Icon = item?.icon as ComponentType<unknown> | undefined;
	const { data, isLoading, error, hasError, pagination, pageSize, isMobile, handlePageSizeChange, setCurrentPage, isEmpty } = useTableComponentType({
		searchTerm,
	});

	const renderTable = () => {
		if (isMobile) {
			return (
				<>
					<ComponentTypeMobileTableView
						data={data}
						isLoading={isLoading}
						hasError={hasError}
						error={error}
						searchTerm={searchTerm}
						pagination={pagination}
						onPageChange={setCurrentPage}
						onPageSizeChange={handlePageSizeChange}
					/>
				</>
			);
		} else {
			return (
				<>
					<ComponentTypeDesktopTableView
						data={data}
						isLoading={isLoading}
						hasError={hasError}
						error={error}
						pageSize={pageSize}
						pagination={pagination}
						onPageChange={setCurrentPage}
						onPageSizeChange={handlePageSizeChange}
					/>
				</>
			);
		}
	};

	return (
		<main className="container mx-auto flex h-full min-h-full flex-1 flex-col space-y-6 px-4 py-1 md:px-2">
			<MainHeaderComponent
				icon={Icon ? <Icon /> : <BotIcon />}
				title="Tipos de componente"
				description="Gerencie o cadastro de tipos de componente"
				search={searchTerm}
				onSearchChange={setSearchTerm}
				total={pagination?.totalItems || 0}
				onAdd={() => onOpenModal()}
				subject={COMPONENT_TYPE_SUBJECTS.COMPONENT_TYPE}
			/>
			<div className="h-full">
				{isEmpty || (data.length === 0 && !isLoading) ? (
					<GenericEmptyState
						buttonText="Adicionar tipo de componente"
						description="Ainda não há tipos de componente cadastrados."
						onAction={() => onOpenModal()}
						title="Nenhum tipo de componente encontrado"
						subject={COMPONENT_TYPE_SUBJECTS.COMPONENT_TYPE}
					/>
				) : (
					renderTable()
				)}
			</div>
		</main>
	);
};
