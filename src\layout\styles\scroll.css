body {
	background: var(--background);
	color: var(--foreground);
	font-family: Arial, Helvetica, sans-serif;
}

* {
	scrollbar-width: thin;
	scrollbar-color: var(--main-color-opacity-50) var(--main-background);
}

*::-webkit-scrollbar {
	width: 8px;
	height: 8px;
}

*::-webkit-scrollbar-track {
	background: var(--secondary-background);
}

*::-webkit-scrollbar-thumb {
	background-color: var(--main-color-opacity-50);
	border-radius: 4px;
}

*::-webkit-scrollbar-thumb:hover {
	background-color: var(--main-color);
}

[data-theme="dark"] *::-webkit-scrollbar-thumb,
[data-theme="dark-green"] *::-webkit-scrollbar-thumb {
	background-color: var(--main-border-opacity-50);
}

[data-theme="dark"] *::-webkit-scrollbar-thumb:hover,
[data-theme="dark-green"] *::-webkit-scrollbar-thumb:hover {
	background-color: var(--main-color);
}

[data-theme="light-green"] *::-webkit-scrollbar-thumb {
	background-color: rgba(0, 160, 60, 0.3);
}

[data-theme="light-green"] *::-webkit-scrollbar-thumb:hover {
	background-color: #00a03c;
}

input:-webkit-autofill,
input:-webkit-autofill:focus {
	transition: background-color 600000s 0s, color 600000s 0s;
}

.hide-scrollbar {
	scrollbar-width: none;
	-ms-overflow-style: none;
}
.hide-scrollbar::-webkit-scrollbar {
	display: none;
}
