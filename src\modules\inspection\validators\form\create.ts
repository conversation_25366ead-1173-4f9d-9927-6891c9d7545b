import z from "zod";

export const createFormSchema = z.object({
	title: z.string().min(1, "O título é obrigatório"),
	text: z.string().optional(),
	nomenclature: z.string().optional(),
	developer: z
		.object({
			id: z.string().min(1, "O ID do elaborador é obrigatório"),
			name: z.string().min(1, "O nome do elaborador é obrigatório"),
		})
		.refine(data => data.id && data.name, {
			message: "O elaborador é obrigatório",
		}),
	approver: z
		.object({
			id: z.string().min(1, "O ID do aprovador é obrigatório"),
			name: z.string().min(1, "O nome do aprovador é obrigatório"),
		})
		.refine(data => data.id && data.name, {
			message: "O aprovador é obrigatório",
		}),
});

export type ICreateForm = z.infer<typeof createFormSchema>;
